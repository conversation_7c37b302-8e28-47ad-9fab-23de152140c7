<?php
/**
 * Helper Functions
 * 
 * @package Toolspick
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get tool card HTML
 */
function toolspick_get_tool_card($tool_id, $show_excerpt = true) {
    $tool = get_post($tool_id);
    if (!$tool || $tool->post_type !== 'tool') {
        return '';
    }

    ob_start();
    global $post;
    $post = $tool;
    setup_postdata($post);
    
    get_template_part('templates/parts/tool-card');
    
    wp_reset_postdata();
    return ob_get_clean();
}

/**
 * Get tool excerpt with custom length
 */
function toolspick_get_tool_excerpt($tool_id, $length = 25) {
    $tool = get_post($tool_id);
    if (!$tool) {
        return '';
    }

    $excerpt = $tool->post_excerpt;
    if (empty($excerpt)) {
        $excerpt = get_field('short_description', $tool_id);
    }
    if (empty($excerpt)) {
        $excerpt = wp_strip_all_tags($tool->post_content);
    }

    if (empty($excerpt)) {
        return '';
    }

    $words = explode(' ', $excerpt);
    if (count($words) > $length) {
        $words = array_slice($words, 0, $length);
        $excerpt = implode(' ', $words) . '...';
    }

    return $excerpt;
}

/**
 * Get tool categories with links
 */
function toolspick_get_tool_categories_html($tool_id, $separator = ', ') {
    $categories = get_the_terms($tool_id, 'tool_category');
    if (!$categories || is_wp_error($categories)) {
        return '';
    }

    $category_links = [];
    foreach ($categories as $category) {
        $category_links[] = sprintf(
            '<a href="%s" class="tool-category-link">%s</a>',
            esc_url(get_term_link($category)),
            esc_html($category->name)
        );
    }

    return implode($separator, $category_links);
}

/**
 * Get tool rating stars HTML
 */
function toolspick_get_rating_stars($rating, $show_number = true) {
    if (!$rating) {
        return '';
    }

    $rating = floatval($rating);
    $full_stars = floor($rating);
    $half_star = ($rating - $full_stars) >= 0.5;
    
    $html = '<div class="tool-rating-stars">';
    
    for ($i = 1; $i <= 5; $i++) {
        if ($i <= $full_stars) {
            $html .= '<span class="star star-full">★</span>';
        } elseif ($i == $full_stars + 1 && $half_star) {
            $html .= '<span class="star star-half">★</span>';
        } else {
            $html .= '<span class="star star-empty">☆</span>';
        }
    }
    
    if ($show_number) {
        $html .= '<span class="rating-number">' . number_format($rating, 1) . '</span>';
    }
    
    $html .= '</div>';
    
    return $html;
}

/**
 * Get tool pricing badge HTML
 */
function toolspick_get_pricing_badge($pricing) {
    if (!$pricing) {
        return '';
    }

    $class = 'pricing-' . strtolower($pricing);
    return sprintf(
        '<span class="tool-pricing %s">%s</span>',
        esc_attr($class),
        esc_html($pricing)
    );
}

/**
 * Get tool platforms HTML
 */
function toolspick_get_platforms_html($platforms) {
    if (!$platforms || !is_array($platforms)) {
        return '';
    }

    $platform_icons = [
        'web' => '🌐',
        'windows' => '🪟',
        'mac' => '🍎',
        'linux' => '🐧',
        'ios' => '📱',
        'android' => '🤖',
        'chrome' => '🔧',
        'firefox' => '🦊',
        'safari' => '🧭'
    ];

    $platform_labels = [
        'web' => 'Web',
        'windows' => 'Windows',
        'mac' => 'macOS',
        'linux' => 'Linux',
        'ios' => 'iOS',
        'android' => 'Android',
        'chrome' => 'Chrome',
        'firefox' => 'Firefox',
        'safari' => 'Safari'
    ];

    $html = '<div class="tool-platforms">';
    foreach ($platforms as $platform) {
        $icon = $platform_icons[$platform] ?? '💻';
        $label = $platform_labels[$platform] ?? ucfirst($platform);
        
        $html .= sprintf(
            '<span class="platform-badge" title="%s">%s %s</span>',
            esc_attr($label),
            $icon,
            esc_html($label)
        );
    }
    $html .= '</div>';

    return $html;
}

/**
 * Get related tools
 */
function toolspick_get_related_tools($tool_id, $limit = 4) {
    $categories = get_the_terms($tool_id, 'tool_category');
    if (!$categories || is_wp_error($categories)) {
        return [];
    }

    $category_ids = wp_list_pluck($categories, 'term_id');

    $args = [
        'post_type' => 'tool',
        'posts_per_page' => $limit,
        'post_status' => 'publish',
        'post__not_in' => [$tool_id],
        'tax_query' => [
            [
                'taxonomy' => 'tool_category',
                'field' => 'term_id',
                'terms' => $category_ids,
                'operator' => 'IN'
            ]
        ],
        'meta_key' => 'tool_votes',
        'orderby' => 'meta_value_num',
        'order' => 'DESC'
    ];

    return get_posts($args);
}

/**
 * Get tool search URL
 */
function toolspick_get_search_url($search_term = '', $category = '', $pricing = '') {
    $url = get_post_type_archive_link('tool');
    $params = [];

    if ($search_term) {
        $params['s'] = $search_term;
    }
    if ($category) {
        $params['tool_category'] = $category;
    }
    if ($pricing) {
        $params['pricing'] = $pricing;
    }

    if (!empty($params)) {
        $url = add_query_arg($params, $url);
    }

    return $url;
}

/**
 * Format large numbers
 */
function toolspick_format_number($number) {
    if ($number >= 1000000) {
        return number_format($number / 1000000, 1) . 'M';
    } elseif ($number >= 1000) {
        return number_format($number / 1000, 1) . 'K';
    }
    return number_format($number);
}

/**
 * Get time ago string
 */
function toolspick_time_ago($date) {
    $time = time() - strtotime($date);

    if ($time < 60) {
        return 'just now';
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
    } elseif ($time < 2592000) {
        $days = floor($time / 86400);
        return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
    } elseif ($time < 31536000) {
        $months = floor($time / 2592000);
        return $months . ' month' . ($months > 1 ? 's' : '') . ' ago';
    } else {
        $years = floor($time / 31536000);
        return $years . ' year' . ($years > 1 ? 's' : '') . ' ago';
    }
}

/**
 * Sanitize tool slug
 */
function toolspick_sanitize_tool_slug($title) {
    $slug = sanitize_title($title);
    
    // Remove common words that don't add value to URLs
    $stop_words = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
    $words = explode('-', $slug);
    $words = array_diff($words, $stop_words);
    
    return implode('-', $words);
}



/**
 * Check if tool is featured
 */
function toolspick_is_tool_featured($tool_id) {
    return get_post_meta($tool_id, 'featured_tool', true) === '1';
}

/**
 * Get breadcrumbs
 */
function toolspick_get_breadcrumbs() {
    if (is_front_page()) {
        return '';
    }

    $breadcrumbs = ['<a href="' . home_url() . '">Home</a>'];

    if (is_singular('tool')) {
        $breadcrumbs[] = '<a href="' . get_post_type_archive_link('tool') . '">Tools</a>';
        
        $categories = get_the_terms(get_the_ID(), 'tool_category');
        if ($categories && !is_wp_error($categories)) {
            $category = $categories[0];
            $breadcrumbs[] = '<a href="' . get_term_link($category) . '">' . $category->name . '</a>';
        }
        
        $breadcrumbs[] = get_the_title();
    } elseif (is_tax('tool_category')) {
        $breadcrumbs[] = '<a href="' . get_post_type_archive_link('tool') . '">Tools</a>';
        $breadcrumbs[] = single_term_title('', false);
    } elseif (is_tax('review_type')) {
        $breadcrumbs[] = '<a href="' . get_post_type_archive_link('tool') . '">Tools</a>';
        $breadcrumbs[] = single_term_title('', false);
    } elseif (is_post_type_archive('tool')) {
        $breadcrumbs[] = 'Tools';
    }

    return '<nav class="breadcrumbs">' . implode(' <span class="separator">›</span> ', $breadcrumbs) . '</nav>';
}

/**
 * AJAX handler for loading directory tools
 */
function toolspick_load_directory_tools() {
    $page = intval($_POST['page'] ?? 1);
    $search = sanitize_text_field($_POST['search'] ?? '');
    $category = sanitize_text_field($_POST['category'] ?? '');
    $sort = sanitize_text_field($_POST['sort'] ?? 'votes');
    $pricing = sanitize_text_field($_POST['pricing'] ?? '');

    $per_page = 12;

    // Build query args
    $args = [
        'post_type' => 'tool',
        'post_status' => 'publish',
        'posts_per_page' => $per_page,
        'paged' => $page,
    ];

    // Search
    if ($search) {
        $args['s'] = $search;
    }

    // Category filter
    if ($category) {
        $args['tax_query'] = [
            [
                'taxonomy' => 'tool_category',
                'field' => 'slug',
                'terms' => $category,
            ],
        ];
    }

    // Pricing filter
    if ($pricing) {
        $pricing_terms = explode(',', $pricing);
        $args['meta_query'] = [
            [
                'key' => 'pricing',
                'value' => $pricing_terms,
                'compare' => 'IN',
            ],
        ];
    }

    // Sorting
    switch ($sort) {
        case 'date':
            $args['orderby'] = 'date';
            $args['order'] = 'DESC';
            break;
        case 'title':
            $args['orderby'] = 'title';
            $args['order'] = 'ASC';
            break;
        case 'rating':
            $args['meta_key'] = 'review_rating';
            $args['orderby'] = 'meta_value_num';
            $args['order'] = 'DESC';
            break;
        default: // votes
            $args['meta_key'] = 'tool_votes';
            $args['orderby'] = 'meta_value_num';
            $args['order'] = 'DESC';
            break;
    }

    $query = new WP_Query($args);

    ob_start();
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            include(get_stylesheet_directory() . '/templates/parts/tool-card.php');
        }
    }
    $html = ob_get_clean();
    wp_reset_postdata();

    // Build response
    $total = $query->found_posts;
    $current_page = $page;
    $start = (($current_page - 1) * $per_page) + 1;
    $end = min($current_page * $per_page, $total);
    $has_more = $current_page * $per_page < $total;

    $title = 'All Tools';
    if ($search) {
        $title = 'Search Results for "' . esc_html($search) . '"';
    } elseif ($category) {
        $term = get_term_by('slug', $category, 'tool_category');
        if ($term) {
            $title = $term->name . ' Tools';
        }
    }

    $results_text = $total > 0 ?
        sprintf('Showing %d-%d of %d tools', $start, $end, $total) :
        'No tools found';

    wp_die(json_encode([
        'success' => true,
        'data' => [
            'html' => $html,
            'total' => $total,
            'has_more' => $has_more,
            'title' => $title,
            'results_text' => $results_text,
        ],
    ]));
}
add_action('wp_ajax_load_directory_tools', 'toolspick_load_directory_tools');
add_action('wp_ajax_nopriv_load_directory_tools', 'toolspick_load_directory_tools');

/**
 * AJAX handler for contact form
 */
function toolspick_handle_contact_form() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['contact_nonce'], 'toolspick_contact_form')) {
        wp_die(json_encode([
            'success' => false,
            'data' => ['message' => 'Security check failed.']
        ]));
    }

    // Sanitize form data
    $name = sanitize_text_field($_POST['contact_name'] ?? '');
    $email = sanitize_email($_POST['contact_email'] ?? '');
    $subject = sanitize_text_field($_POST['contact_subject'] ?? '');
    $message = sanitize_textarea_field($_POST['contact_message'] ?? '');
    $subscribe = isset($_POST['subscribe_newsletter']);

    // Validate required fields
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        wp_die(json_encode([
            'success' => false,
            'data' => ['message' => 'Please fill in all required fields.']
        ]));
    }

    if (!is_email($email)) {
        wp_die(json_encode([
            'success' => false,
            'data' => ['message' => 'Please enter a valid email address.']
        ]));
    }

    // Prepare email
    $admin_email = get_option('admin_email');
    $site_name = get_bloginfo('name');

    $subject_labels = [
        'general' => 'General Inquiry',
        'tool_submission' => 'Tool Submission',
        'tool_update' => 'Tool Update Request',
        'partnership' => 'Partnership Opportunity',
        'bug_report' => 'Bug Report',
        'other' => 'Other Inquiry'
    ];

    $email_subject = sprintf('[%s] %s from %s',
        $site_name,
        $subject_labels[$subject] ?? 'Contact Form',
        $name
    );

    $email_message = sprintf(
        "New contact form submission:\n\n" .
        "Name: %s\n" .
        "Email: %s\n" .
        "Subject: %s\n" .
        "Newsletter Signup: %s\n\n" .
        "Message:\n%s\n\n" .
        "---\n" .
        "Sent from: %s\n" .
        "IP Address: %s\n" .
        "User Agent: %s",
        $name,
        $email,
        $subject_labels[$subject] ?? $subject,
        $subscribe ? 'Yes' : 'No',
        $message,
        home_url(),
        $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
        $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
    );

    $headers = [
        'Content-Type: text/plain; charset=UTF-8',
        'From: ' . $site_name . ' <' . $admin_email . '>',
        'Reply-To: ' . $name . ' <' . $email . '>'
    ];

    // Send email
    $sent = wp_mail($admin_email, $email_subject, $email_message, $headers);

    if ($sent) {
        // Log the contact form submission
        error_log(sprintf('Contact form submitted by %s (%s) - Subject: %s', $name, $email, $subject));

        // Handle newsletter signup if requested
        if ($subscribe) {
            // You can integrate with your newsletter service here
            do_action('toolspick_newsletter_signup', $email, $name);
        }

        wp_die(json_encode([
            'success' => true,
            'data' => ['message' => 'Thank you! Your message has been sent successfully.']
        ]));
    } else {
        wp_die(json_encode([
            'success' => false,
            'data' => ['message' => 'Sorry, there was an error sending your message. Please try again.']
        ]));
    }
}
add_action('wp_ajax_toolspick_contact_form', 'toolspick_handle_contact_form');
add_action('wp_ajax_nopriv_toolspick_contact_form', 'toolspick_handle_contact_form');

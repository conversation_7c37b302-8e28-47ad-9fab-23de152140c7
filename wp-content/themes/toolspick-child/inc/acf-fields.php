<?php
/**
 * ACF Fields Configuration
 * 
 * @package Toolspick
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Set ACF JSON save and load paths
 */
function toolspick_acf_json_save_point($path) {
    return get_stylesheet_directory() . '/acf-json';
}
add_filter('acf/settings/save_json', 'toolspick_acf_json_save_point');

function toolspick_acf_json_load_point($paths) {
    $paths[] = get_stylesheet_directory() . '/acf-json';
    return $paths;
}
add_filter('acf/settings/load_json', 'toolspick_acf_json_load_point');

/**
 * Register ACF field groups programmatically
 */
function toolspick_register_acf_fields() {
    if (!function_exists('acf_add_local_field_group')) {
        return;
    }

    // Tool Details Field Group
    acf_add_local_field_group([
        'key' => 'group_tool_details',
        'title' => 'Tool Details',
        'fields' => [
            [
                'key' => 'field_short_description',
                'label' => 'Short Description',
                'name' => 'short_description',
                'type' => 'textarea',
                'instructions' => 'Brief description of the tool (max 160 characters for SEO)',
                'required' => 1,
                'maxlength' => 160,
                'rows' => 3,
            ],
            [
                'key' => 'field_official_website',
                'label' => 'Official Website',
                'name' => 'official_website',
                'type' => 'url',
                'instructions' => 'The official website URL of the tool',
                'required' => 1,
            ],
            [
                'key' => 'field_pricing',
                'label' => 'Pricing Model',
                'name' => 'pricing',
                'type' => 'select',
                'instructions' => 'Select the pricing model for this tool',
                'required' => 1,
                'choices' => [
                    'Free' => 'Free',
                    'Freemium' => 'Freemium',
                    'Paid' => 'Paid',
                ],
                'default_value' => 'Freemium',
            ],
            [
                'key' => 'field_pricing_details',
                'label' => 'Pricing Details',
                'name' => 'pricing_details',
                'type' => 'textarea',
                'instructions' => 'Detailed pricing information',
                'rows' => 4,
            ],
            [
                'key' => 'field_features',
                'label' => 'Key Features',
                'name' => 'features',
                'type' => 'repeater',
                'instructions' => 'Add key features of the tool',
                'min' => 1,
                'max' => 10,
                'layout' => 'table',
                'button_label' => 'Add Feature',
                'sub_fields' => [
                    [
                        'key' => 'field_feature_text',
                        'label' => 'Feature',
                        'name' => 'feature_text',
                        'type' => 'text',
                        'required' => 1,
                    ],
                    [
                        'key' => 'field_feature_description',
                        'label' => 'Feature Description',
                        'name' => 'feature_description',
                        'type' => 'textarea',
                        'rows' => 2,
                        'instructions' => 'Optional detailed description of the feature',
                    ],
                ],
            ],
            [
                'key' => 'field_supported_platforms',
                'label' => 'Supported Platforms',
                'name' => 'supported_platforms',
                'type' => 'checkbox',
                'instructions' => 'Select all platforms that support this tool',
                'choices' => [
                    'web' => 'Web Browser',
                    'windows' => 'Windows',
                    'mac' => 'macOS',
                    'linux' => 'Linux',
                    'ios' => 'iOS',
                    'android' => 'Android',
                    'chrome' => 'Chrome Extension',
                    'firefox' => 'Firefox Extension',
                    'safari' => 'Safari Extension',
                ],
                'layout' => 'vertical',
            ],
            [
                'key' => 'field_integrations',
                'label' => 'Integrations',
                'name' => 'integrations',
                'type' => 'textarea',
                'instructions' => 'List of tools and services this integrates with (comma-separated)',
                'rows' => 3,
            ],
            [
                'key' => 'field_alternatives',
                'label' => 'Alternative Tools',
                'name' => 'alternatives',
                'type' => 'relationship',
                'instructions' => 'Select alternative/competitor tools',
                'post_type' => ['tool'],
                'filters' => ['search'],
                'return_format' => 'id',
                'min' => 0,
                'max' => 10,
            ],
            [
                'key' => 'field_tool_gallery',
                'label' => 'Tool Gallery/Screenshots',
                'name' => 'tool_gallery',
                'type' => 'gallery',
                'instructions' => 'Upload screenshots or images of the tool',
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
            ],
            [
                'key' => 'field_use_cases',
                'label' => 'Use Cases',
                'name' => 'use_cases',
                'type' => 'repeater',
                'instructions' => 'Add different use cases for this tool',
                'sub_fields' => [
                    [
                        'key' => 'field_use_case_title',
                        'label' => 'Use Case Title',
                        'name' => 'title',
                        'type' => 'text',
                        'required' => 1,
                    ],
                    [
                        'key' => 'field_use_case_description',
                        'label' => 'Use Case Description',
                        'name' => 'description',
                        'type' => 'textarea',
                        'rows' => 3,
                        'required' => 1,
                    ],
                ],
                'min' => 0,
                'max' => 10,
                'layout' => 'table',
                'button_label' => 'Add Use Case',
            ],
            [
                'key' => 'field_review_rating',
                'label' => 'Review Rating',
                'name' => 'review_rating',
                'type' => 'number',
                'instructions' => 'Overall rating out of 5',
                'min' => 0,
                'max' => 5,
                'step' => 0.1,
                'default_value' => 4.0,
            ],
            [
                'key' => 'field_video_tutorial',
                'label' => 'Video Tutorial',
                'name' => 'video_tutorial',
                'type' => 'url',
                'instructions' => 'YouTube or Vimeo URL for tutorial video',
            ],
            [
                'key' => 'field_affiliate_link',
                'label' => 'Affiliate Link',
                'name' => 'affiliate_link',
                'type' => 'url',
                'instructions' => 'Affiliate or referral link (optional)',
            ],
            [
                'key' => 'field_tags',
                'label' => 'Tags',
                'name' => 'tags',
                'type' => 'text',
                'instructions' => 'Comma-separated tags for better categorization',
            ],
            [
                'key' => 'field_launch_date',
                'label' => 'Launch Date',
                'name' => 'launch_date',
                'type' => 'date_picker',
                'instructions' => 'When was this tool launched?',
                'display_format' => 'F j, Y',
                'return_format' => 'Y-m-d',
            ],
            [
                'key' => 'field_company_size',
                'label' => 'Company Size',
                'name' => 'company_size',
                'type' => 'select',
                'instructions' => 'Size of the company behind this tool',
                'choices' => [
                    'startup' => 'Startup (1-10 employees)',
                    'small' => 'Small (11-50 employees)',
                    'medium' => 'Medium (51-200 employees)',
                    'large' => 'Large (201-1000 employees)',
                    'enterprise' => 'Enterprise (1000+ employees)',
                ],
                'allow_null' => 1,
            ],
            [
                'key' => 'field_free_trial',
                'label' => 'Free Trial Available',
                'name' => 'free_trial',
                'type' => 'true_false',
                'instructions' => 'Does this tool offer a free trial?',
                'default_value' => 0,
            ],
            [
                'key' => 'field_trial_duration',
                'label' => 'Trial Duration',
                'name' => 'trial_duration',
                'type' => 'text',
                'instructions' => 'How long is the free trial? (e.g., "14 days", "30 days")',
                'conditional_logic' => [
                    [
                        [
                            'field' => 'field_free_trial',
                            'operator' => '==',
                            'value' => '1',
                        ],
                    ],
                ],
            ],
        ],
        'location' => [
            [
                [
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'tool',
                ],
            ],
        ],
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
    ]);

    // SEO & Schema Field Group
    acf_add_local_field_group([
        'key' => 'group_tool_seo',
        'title' => 'SEO & Schema',
        'fields' => [
            [
                'key' => 'field_meta_title',
                'label' => 'Meta Title',
                'name' => 'meta_title',
                'type' => 'text',
                'instructions' => 'Custom meta title (leave empty to use default)',
                'maxlength' => 60,
            ],
            [
                'key' => 'field_meta_description',
                'label' => 'Meta Description',
                'name' => 'meta_description',
                'type' => 'textarea',
                'instructions' => 'Custom meta description (leave empty to use short description)',
                'maxlength' => 160,
                'rows' => 3,
            ],
            [
                'key' => 'field_schema_type',
                'label' => 'Schema Type',
                'name' => 'schema_type',
                'type' => 'select',
                'instructions' => 'Select the appropriate schema type',
                'choices' => [
                    'SoftwareApplication' => 'Software Application',
                    'WebApplication' => 'Web Application',
                    'MobileApplication' => 'Mobile Application',
                    'Product' => 'Product',
                ],
                'default_value' => 'SoftwareApplication',
            ],
            [
                'key' => 'field_application_category',
                'label' => 'Application Category',
                'name' => 'application_category',
                'type' => 'text',
                'instructions' => 'Schema.org application category (e.g., "BusinessApplication", "DesignApplication")',
            ],
            [
                'key' => 'field_operating_system',
                'label' => 'Operating System',
                'name' => 'operating_system',
                'type' => 'text',
                'instructions' => 'Supported operating systems for schema (e.g., "Windows, macOS, Linux")',
            ],
        ],
        'location' => [
            [
                [
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'tool',
                ],
            ],
        ],
        'menu_order' => 1,
        'position' => 'side',
        'style' => 'default',
    ]);
}
add_action('acf/init', 'toolspick_register_acf_fields');

/**
 * Register Page Template Fields
 */
function toolspick_register_page_template_fields() {
    if (!function_exists('acf_add_local_field_group')) {
        return;
    }

    // Homepage Template Fields
    acf_add_local_field_group([
        'key' => 'group_homepage_template',
        'title' => 'Homepage Settings',
        'fields' => [
            [
                'key' => 'field_hero_title',
                'label' => 'Hero Title',
                'name' => 'hero_title',
                'type' => 'text',
                'instructions' => 'Main headline for the hero section',
                'default_value' => 'Discover the Best AI & SaaS Tools',
            ],
            [
                'key' => 'field_hero_subtitle',
                'label' => 'Hero Subtitle',
                'name' => 'hero_subtitle',
                'type' => 'textarea',
                'instructions' => 'Subtitle text for the hero section',
                'rows' => 3,
                'default_value' => 'Find, compare, and choose the perfect tools for your business.',
            ],
            [
                'key' => 'field_categories_title',
                'label' => 'Categories Section Title',
                'name' => 'categories_title',
                'type' => 'text',
                'default_value' => 'Popular Categories',
            ],
            [
                'key' => 'field_categories_subtitle',
                'label' => 'Categories Section Subtitle',
                'name' => 'categories_subtitle',
                'type' => 'text',
                'default_value' => 'Explore tools by category to find exactly what you need',
            ],
            [
                'key' => 'field_latest_tools_title',
                'label' => 'Latest Tools Title',
                'name' => 'latest_tools_title',
                'type' => 'text',
                'default_value' => 'Latest Tools',
            ],
            [
                'key' => 'field_latest_tools_subtitle',
                'label' => 'Latest Tools Subtitle',
                'name' => 'latest_tools_subtitle',
                'type' => 'text',
                'default_value' => 'Recently added tools to our directory',
            ],
            [
                'key' => 'field_latest_tools_count',
                'label' => 'Latest Tools Count',
                'name' => 'latest_tools_count',
                'type' => 'number',
                'default_value' => 6,
                'min' => 1,
                'max' => 20,
            ],
            [
                'key' => 'field_show_top_rated_section',
                'label' => 'Show Top Rated Section',
                'name' => 'show_top_rated_section',
                'type' => 'true_false',
                'default_value' => 1,
            ],
            [
                'key' => 'field_top_rated_title',
                'label' => 'Top Rated Title',
                'name' => 'top_rated_title',
                'type' => 'text',
                'default_value' => 'Top Rated Tools',
                'conditional_logic' => [
                    [
                        [
                            'field' => 'field_show_top_rated_section',
                            'operator' => '==',
                            'value' => '1',
                        ],
                    ],
                ],
            ],
            [
                'key' => 'field_top_rated_subtitle',
                'label' => 'Top Rated Subtitle',
                'name' => 'top_rated_subtitle',
                'type' => 'text',
                'default_value' => 'Most popular tools based on user votes',
                'conditional_logic' => [
                    [
                        [
                            'field' => 'field_show_top_rated_section',
                            'operator' => '==',
                            'value' => '1',
                        ],
                    ],
                ],
            ],
            [
                'key' => 'field_top_rated_count',
                'label' => 'Top Rated Count',
                'name' => 'top_rated_count',
                'type' => 'number',
                'default_value' => 6,
                'min' => 1,
                'max' => 20,
                'conditional_logic' => [
                    [
                        [
                            'field' => 'field_show_top_rated_section',
                            'operator' => '==',
                            'value' => '1',
                        ],
                    ],
                ],
            ],
            [
                'key' => 'field_show_newsletter_section',
                'label' => 'Show Newsletter Section',
                'name' => 'show_newsletter_section',
                'type' => 'true_false',
                'default_value' => 1,
            ],
            [
                'key' => 'field_newsletter_title',
                'label' => 'Newsletter Title',
                'name' => 'newsletter_title',
                'type' => 'text',
                'default_value' => 'Stay Updated',
                'conditional_logic' => [
                    [
                        [
                            'field' => 'field_show_newsletter_section',
                            'operator' => '==',
                            'value' => '1',
                        ],
                    ],
                ],
            ],
            [
                'key' => 'field_newsletter_subtitle',
                'label' => 'Newsletter Subtitle',
                'name' => 'newsletter_subtitle',
                'type' => 'text',
                'default_value' => 'Get notified about new tools and updates',
                'conditional_logic' => [
                    [
                        [
                            'field' => 'field_show_newsletter_section',
                            'operator' => '==',
                            'value' => '1',
                        ],
                    ],
                ],
            ],
            [
                'key' => 'field_show_stats_section',
                'label' => 'Show Statistics Section',
                'name' => 'show_stats_section',
                'type' => 'true_false',
                'default_value' => 1,
            ],
        ],
        'location' => [
            [
                [
                    'param' => 'page_template',
                    'operator' => '==',
                    'value' => 'page-templates/homepage-template.php',
                ],
            ],
        ],
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
    ]);

    // Tools Directory Template Fields
    acf_add_local_field_group([
        'key' => 'group_tools_directory_template',
        'title' => 'Tools Directory Settings',
        'fields' => [
            [
                'key' => 'field_directory_title',
                'label' => 'Directory Title',
                'name' => 'directory_title',
                'type' => 'text',
                'default_value' => 'All Tools',
            ],
            [
                'key' => 'field_directory_description',
                'label' => 'Directory Description',
                'name' => 'directory_description',
                'type' => 'textarea',
                'rows' => 3,
                'default_value' => 'Discover the best AI and SaaS tools for your business.',
            ],
            [
                'key' => 'field_show_categories_section',
                'label' => 'Show Categories Section',
                'name' => 'show_categories_section',
                'type' => 'true_false',
                'default_value' => 1,
            ],
            [
                'key' => 'field_categories_section_title',
                'label' => 'Categories Section Title',
                'name' => 'categories_section_title',
                'type' => 'text',
                'default_value' => 'Browse by Category',
                'conditional_logic' => [
                    [
                        [
                            'field' => 'field_show_categories_section',
                            'operator' => '==',
                            'value' => '1',
                        ],
                    ],
                ],
            ],
            [
                'key' => 'field_categories_to_show',
                'label' => 'Number of Categories to Show',
                'name' => 'categories_to_show',
                'type' => 'number',
                'default_value' => 8,
                'min' => 1,
                'max' => 20,
                'conditional_logic' => [
                    [
                        [
                            'field' => 'field_show_categories_section',
                            'operator' => '==',
                            'value' => '1',
                        ],
                    ],
                ],
            ],
        ],
        'location' => [
            [
                [
                    'param' => 'page_template',
                    'operator' => '==',
                    'value' => 'page-templates/tools-directory-template.php',
                ],
            ],
        ],
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
    ]);

    // Category Showcase Template Fields
    acf_add_local_field_group([
        'key' => 'group_category_showcase_template',
        'title' => 'Category Showcase Settings',
        'fields' => [
            [
                'key' => 'field_showcase_title',
                'label' => 'Showcase Title',
                'name' => 'showcase_title',
                'type' => 'text',
                'default_value' => 'Tool Categories',
            ],
            [
                'key' => 'field_showcase_description',
                'label' => 'Showcase Description',
                'name' => 'showcase_description',
                'type' => 'textarea',
                'rows' => 3,
                'default_value' => 'Explore our comprehensive collection of tool categories.',
            ],
            [
                'key' => 'field_show_featured_categories',
                'label' => 'Show Featured Categories Section',
                'name' => 'show_featured_categories',
                'type' => 'true_false',
                'default_value' => 1,
            ],
            [
                'key' => 'field_featured_section_title',
                'label' => 'Featured Section Title',
                'name' => 'featured_section_title',
                'type' => 'text',
                'default_value' => 'Featured Categories',
                'conditional_logic' => [
                    [
                        [
                            'field' => 'field_show_featured_categories',
                            'operator' => '==',
                            'value' => '1',
                        ],
                    ],
                ],
            ],
            [
                'key' => 'field_featured_section_subtitle',
                'label' => 'Featured Section Subtitle',
                'name' => 'featured_section_subtitle',
                'type' => 'text',
                'default_value' => 'Most popular tool categories',
                'conditional_logic' => [
                    [
                        [
                            'field' => 'field_show_featured_categories',
                            'operator' => '==',
                            'value' => '1',
                        ],
                    ],
                ],
            ],
            [
                'key' => 'field_featured_categories_count',
                'label' => 'Featured Categories Count',
                'name' => 'featured_categories_count',
                'type' => 'number',
                'default_value' => 6,
                'min' => 1,
                'max' => 12,
                'conditional_logic' => [
                    [
                        [
                            'field' => 'field_show_featured_categories',
                            'operator' => '==',
                            'value' => '1',
                        ],
                    ],
                ],
            ],
            [
                'key' => 'field_all_categories_title',
                'label' => 'All Categories Title',
                'name' => 'all_categories_title',
                'type' => 'text',
                'default_value' => 'All Categories',
            ],
            [
                'key' => 'field_all_categories_subtitle',
                'label' => 'All Categories Subtitle',
                'name' => 'all_categories_subtitle',
                'type' => 'text',
                'default_value' => 'Browse all available tool categories',
            ],
            [
                'key' => 'field_show_statistics_section',
                'label' => 'Show Statistics Section',
                'name' => 'show_statistics_section',
                'type' => 'true_false',
                'default_value' => 1,
            ],
            [
                'key' => 'field_statistics_title',
                'label' => 'Statistics Title',
                'name' => 'statistics_title',
                'type' => 'text',
                'default_value' => 'Category Statistics',
                'conditional_logic' => [
                    [
                        [
                            'field' => 'field_show_statistics_section',
                            'operator' => '==',
                            'value' => '1',
                        ],
                    ],
                ],
            ],
        ],
        'location' => [
            [
                [
                    'param' => 'page_template',
                    'operator' => '==',
                    'value' => 'page-templates/category-showcase-template.php',
                ],
            ],
        ],
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
    ]);

    // Single Tool Template Fields
    acf_add_local_field_group([
        'key' => 'group_single_tool_template',
        'title' => 'Single Tool Settings',
        'fields' => [
            [
                'key' => 'field_selected_tool',
                'label' => 'Select Tool',
                'name' => 'selected_tool',
                'type' => 'post_object',
                'instructions' => 'Choose which tool to display on this page',
                'required' => 1,
                'post_type' => ['tool'],
                'return_format' => 'id',
                'ui' => 1,
            ],
        ],
        'location' => [
            [
                [
                    'param' => 'page_template',
                    'operator' => '==',
                    'value' => 'page-templates/single-tool-template.php',
                ],
            ],
        ],
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
    ]);

    // Contact Template Fields
    acf_add_local_field_group([
        'key' => 'group_contact_template',
        'title' => 'Contact Page Settings',
        'fields' => [
            [
                'key' => 'field_contact_title',
                'label' => 'Contact Title',
                'name' => 'contact_title',
                'type' => 'text',
                'default_value' => 'Get in Touch',
            ],
            [
                'key' => 'field_contact_description',
                'label' => 'Contact Description',
                'name' => 'contact_description',
                'type' => 'textarea',
                'rows' => 3,
                'default_value' => 'Have a question about our tools directory? Want to submit a tool? We\'d love to hear from you.',
            ],
            [
                'key' => 'field_form_title',
                'label' => 'Form Title',
                'name' => 'form_title',
                'type' => 'text',
                'default_value' => 'Send us a Message',
            ],
            [
                'key' => 'field_info_title',
                'label' => 'Info Section Title',
                'name' => 'info_title',
                'type' => 'text',
                'default_value' => 'Contact Information',
            ],
            [
                'key' => 'field_show_newsletter_signup',
                'label' => 'Show Newsletter Signup',
                'name' => 'show_newsletter_signup',
                'type' => 'true_false',
                'default_value' => 1,
            ],
            [
                'key' => 'field_show_email_info',
                'label' => 'Show Email Info Card',
                'name' => 'show_email_info',
                'type' => 'true_false',
                'default_value' => 1,
            ],
            [
                'key' => 'field_contact_email',
                'label' => 'Contact Email',
                'name' => 'contact_email',
                'type' => 'email',
                'default_value' => '<EMAIL>',
                'conditional_logic' => [
                    [
                        [
                            'field' => 'field_show_email_info',
                            'operator' => '==',
                            'value' => '1',
                        ],
                    ],
                ],
            ],
            [
                'key' => 'field_show_social_info',
                'label' => 'Show Social Info Card',
                'name' => 'show_social_info',
                'type' => 'true_false',
                'default_value' => 1,
            ],
            [
                'key' => 'field_twitter_url',
                'label' => 'Twitter URL',
                'name' => 'twitter_url',
                'type' => 'url',
                'conditional_logic' => [
                    [
                        [
                            'field' => 'field_show_social_info',
                            'operator' => '==',
                            'value' => '1',
                        ],
                    ],
                ],
            ],
            [
                'key' => 'field_linkedin_url',
                'label' => 'LinkedIn URL',
                'name' => 'linkedin_url',
                'type' => 'url',
                'conditional_logic' => [
                    [
                        [
                            'field' => 'field_show_social_info',
                            'operator' => '==',
                            'value' => '1',
                        ],
                    ],
                ],
            ],
            [
                'key' => 'field_show_response_info',
                'label' => 'Show Response Time Card',
                'name' => 'show_response_info',
                'type' => 'true_false',
                'default_value' => 1,
            ],
            [
                'key' => 'field_response_time',
                'label' => 'Response Time',
                'name' => 'response_time',
                'type' => 'text',
                'default_value' => '24 hours',
                'conditional_logic' => [
                    [
                        [
                            'field' => 'field_show_response_info',
                            'operator' => '==',
                            'value' => '1',
                        ],
                    ],
                ],
            ],
            [
                'key' => 'field_show_faq_section',
                'label' => 'Show FAQ Section',
                'name' => 'show_faq_section',
                'type' => 'true_false',
                'default_value' => 1,
            ],
        ],
        'location' => [
            [
                [
                    'param' => 'page_template',
                    'operator' => '==',
                    'value' => 'page-templates/contact-template.php',
                ],
            ],
        ],
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
    ]);
}
add_action('acf/init', 'toolspick_register_page_template_fields');

/**
 * Add custom ACF admin styles
 */
function toolspick_acf_admin_styles() {
    ?>
    <style>
        .acf-field[data-name="short_description"] textarea {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        .acf-field[data-name="features"] .acf-repeater {
            max-width: 100%;
        }
        .acf-field[data-name="review_rating"] input[type="number"] {
            width: 80px;
        }
        .acf-field-message {
            background: #f0f6fc;
            border-left: 4px solid #0073aa;
            padding: 12px;
            margin: 20px 0;
        }
    </style>
    <?php
}
add_action('acf/input/admin_head', 'toolspick_acf_admin_styles');

/**
 * Validate ACF fields
 */
function toolspick_validate_acf_fields($valid, $value, $field, $input) {
    if (!$valid) {
        return $valid;
    }

    switch ($field['name']) {
        case 'short_description':
            if (strlen($value) > 160) {
                $valid = 'Short description must be 160 characters or less.';
            }
            break;
            
        case 'review_rating':
            if ($value < 0 || $value > 5) {
                $valid = 'Rating must be between 0 and 5.';
            }
            break;
    }

    return $valid;
}
add_filter('acf/validate_value', 'toolspick_validate_acf_fields', 10, 4);

/**
 * Auto-populate some fields
 */
function toolspick_auto_populate_fields($value, $post_id, $field) {
    // Auto-generate meta title if empty
    if ($field['name'] === 'meta_title' && empty($value)) {
        $title = get_the_title($post_id);
        if ($title) {
            $value = $title . ' - AI Tool Review & Guide | Toolspick';
        }
    }
    
    // Auto-generate meta description if empty
    if ($field['name'] === 'meta_description' && empty($value)) {
        $short_desc = get_field('short_description', $post_id);
        if ($short_desc) {
            $value = $short_desc;
        }
    }
    
    return $value;
}
add_filter('acf/load_value', 'toolspick_auto_populate_fields', 10, 3);

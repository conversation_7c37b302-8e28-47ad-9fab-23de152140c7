<?php
/**
 * SEO and Schema Markup
 * 
 * @package Toolspick
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add schema markup to head
 */
function toolspick_add_schema_markup() {
    if (is_singular('tool')) {
        echo toolspick_get_tool_schema(get_the_ID());
    } elseif (is_tax('tool_category')) {
        echo toolspick_get_category_schema();
    } elseif (is_tax('review_type')) {
        echo toolspick_get_review_type_schema();
    } elseif (is_post_type_archive('tool')) {
        echo toolspick_get_tools_archive_schema();
    } elseif (is_front_page()) {
        echo toolspick_get_website_schema();
    }
}
add_action('wp_head', 'toolspick_add_schema_markup');

/**
 * Get tool schema markup (enhanced version from helpers.php)
 */
function toolspick_get_tool_schema($tool_id) {
    $tool = get_post($tool_id);
    if (!$tool) {
        return '';
    }

    $schema_type = get_field('schema_type', $tool_id) ?: 'SoftwareApplication';
    $short_description = get_field('short_description', $tool_id);
    $official_website = get_field('official_website', $tool_id);
    $pricing = get_field('pricing', $tool_id);
    $pricing_details = get_field('pricing_details', $tool_id);
    $rating = get_field('review_rating', $tool_id);
    $supported_platforms = get_field('supported_platforms', $tool_id);
    $application_category = get_field('application_category', $tool_id);
    $operating_system = get_field('operating_system', $tool_id);
    $features = get_field('features', $tool_id);
    $launch_date = get_field('launch_date', $tool_id);
    $votes = get_post_meta($tool_id, 'tool_votes', true) ?: 0;
    $categories = get_the_terms($tool_id, 'tool_category');

    $schema = [
        '@context' => 'https://schema.org',
        '@type' => $schema_type,
        'name' => $tool->post_title,
        'description' => $short_description ?: wp_strip_all_tags($tool->post_content),
        'url' => get_permalink($tool_id),
        'datePublished' => $tool->post_date,
        'dateModified' => $tool->post_modified,
        'author' => [
            '@type' => 'Organization',
            'name' => get_bloginfo('name'),
            'url' => home_url()
        ]
    ];

    if ($official_website) {
        $schema['sameAs'] = $official_website;
        $schema['mainEntityOfPage'] = $official_website;
    }

    if (has_post_thumbnail($tool_id)) {
        $schema['image'] = [
            '@type' => 'ImageObject',
            'url' => get_the_post_thumbnail_url($tool_id, 'full'),
            'width' => 1200,
            'height' => 630
        ];
    }

    if ($application_category) {
        $schema['applicationCategory'] = $application_category;
    } elseif ($categories && !is_wp_error($categories)) {
        $schema['applicationCategory'] = $categories[0]->name;
    }

    if ($operating_system) {
        $schema['operatingSystem'] = $operating_system;
    } elseif ($supported_platforms && is_array($supported_platforms)) {
        $os_map = [
            'windows' => 'Windows',
            'mac' => 'macOS',
            'linux' => 'Linux',
            'ios' => 'iOS',
            'android' => 'Android',
            'web' => 'Web Browser'
        ];

        $os_list = [];
        foreach ($supported_platforms as $platform) {
            if (isset($os_map[$platform])) {
                $os_list[] = $os_map[$platform];
            }
        }

        if (!empty($os_list)) {
            $schema['operatingSystem'] = implode(', ', array_unique($os_list));
        }
    }

    // Features
    if ($features && is_array($features)) {
        $feature_list = [];
        foreach ($features as $feature) {
            if (isset($feature['feature_text'])) {
                $feature_list[] = $feature['feature_text'];
            }
        }
        if (!empty($feature_list)) {
            $schema['featureList'] = $feature_list;
        }
    }

    // Pricing/Offers
    if ($pricing) {
        $offer = [
            '@type' => 'Offer',
            'availability' => 'https://schema.org/InStock',
            'url' => $official_website ?: get_permalink($tool_id)
        ];

        if ($pricing === 'Free') {
            $offer['price'] = '0';
            $offer['priceCurrency'] = 'USD';
        } elseif ($pricing_details) {
            $offer['description'] = $pricing_details;
        }

        $schema['offers'] = $offer;
    }

    // Rating
    if ($rating && $votes > 0) {
        $schema['aggregateRating'] = [
            '@type' => 'AggregateRating',
            'ratingValue' => $rating,
            'bestRating' => '5',
            'worstRating' => '1',
            'ratingCount' => $votes
        ];
    }

    // Release date
    if ($launch_date) {
        $schema['dateCreated'] = $launch_date;
        $schema['releaseDate'] = $launch_date;
    }

    // Publisher
    $schema['publisher'] = [
        '@type' => 'Organization',
        'name' => get_bloginfo('name'),
        'url' => home_url(),
        'logo' => [
            '@type' => 'ImageObject',
            'url' => get_site_icon_url(512) ?: (get_template_directory_uri() . '/assets/images/logo.png')
        ]
    ];

    return '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) . '</script>' . "\n";
}

/**
 * Get category schema markup
 */
function toolspick_get_category_schema() {
    $term = get_queried_object();
    if (!$term) {
        return '';
    }

    $schema = [
        '@context' => 'https://schema.org',
        '@type' => 'CollectionPage',
        'name' => $term->name . ' Tools',
        'description' => $term->description ?: "Discover the best {$term->name} tools and software solutions.",
        'url' => get_term_link($term),
        'mainEntity' => [
            '@type' => 'ItemList',
            'name' => $term->name . ' Tools',
            'numberOfItems' => $term->count,
            'itemListElement' => []
        ],
        'breadcrumb' => [
            '@type' => 'BreadcrumbList',
            'itemListElement' => [
                [
                    '@type' => 'ListItem',
                    'position' => 1,
                    'name' => 'Home',
                    'item' => home_url()
                ],
                [
                    '@type' => 'ListItem',
                    'position' => 2,
                    'name' => 'Tools',
                    'item' => get_post_type_archive_link('tool')
                ],
                [
                    '@type' => 'ListItem',
                    'position' => 3,
                    'name' => $term->name,
                    'item' => get_term_link($term)
                ]
            ]
        ]
    ];

    // Add tools to the list
    $tools = get_posts([
        'post_type' => 'tool',
        'posts_per_page' => 10,
        'tax_query' => [
            [
                'taxonomy' => 'tool_category',
                'field' => 'term_id',
                'terms' => $term->term_id
            ]
        ]
    ]);

    foreach ($tools as $index => $tool) {
        $schema['mainEntity']['itemListElement'][] = [
            '@type' => 'ListItem',
            'position' => $index + 1,
            'item' => [
                '@type' => 'SoftwareApplication',
                'name' => $tool->post_title,
                'url' => get_permalink($tool->ID),
                'description' => get_field('short_description', $tool->ID) ?: wp_trim_words($tool->post_content, 20)
            ]
        ];
    }

    return '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) . '</script>' . "\n";
}

/**
 * Get review type schema markup
 */
function toolspick_get_review_type_schema() {
    $term = get_queried_object();
    if (!$term) {
        return '';
    }

    $schema = [
        '@context' => 'https://schema.org',
        '@type' => 'CollectionPage',
        'name' => $term->name,
        'description' => $term->description ?: "Browse tools with {$term->name} content.",
        'url' => get_term_link($term),
        'mainEntity' => [
            '@type' => 'ItemList',
            'name' => $term->name,
            'numberOfItems' => $term->count
        ]
    ];

    return '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) . '</script>' . "\n";
}

/**
 * Get tools archive schema markup
 */
function toolspick_get_tools_archive_schema() {
    $schema = [
        '@context' => 'https://schema.org',
        '@type' => 'CollectionPage',
        'name' => 'AI & SaaS Tools Directory',
        'description' => 'Comprehensive directory of AI and SaaS tools with reviews, alternatives, and guides.',
        'url' => get_post_type_archive_link('tool'),
        'mainEntity' => [
            '@type' => 'ItemList',
            'name' => 'Tools Directory',
            'numberOfItems' => wp_count_posts('tool')->publish
        ]
    ];

    return '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) . '</script>' . "\n";
}

/**
 * Get website schema markup
 */
function toolspick_get_website_schema() {
    $schema = [
        '@context' => 'https://schema.org',
        '@type' => 'WebSite',
        'name' => get_bloginfo('name'),
        'description' => get_bloginfo('description'),
        'url' => home_url(),
        'potentialAction' => [
            '@type' => 'SearchAction',
            'target' => [
                '@type' => 'EntryPoint',
                'urlTemplate' => home_url('/?s={search_term_string}&post_type=tool')
            ],
            'query-input' => 'required name=search_term_string'
        ],
        'publisher' => [
            '@type' => 'Organization',
            'name' => get_bloginfo('name'),
            'url' => home_url(),
            'logo' => [
                '@type' => 'ImageObject',
                'url' => get_site_icon_url(512) ?: (get_template_directory_uri() . '/assets/images/logo.png')
            ]
        ]
    ];

    return '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) . '</script>' . "\n";
}

/**
 * Customize meta titles
 */
function toolspick_custom_meta_title($title) {
    if (is_singular('tool')) {
        $custom_title = get_field('meta_title');
        if ($custom_title) {
            return $custom_title;
        }
        
        $tool_title = get_the_title();
        $categories = get_the_terms(get_the_ID(), 'tool_category');
        $category_name = $categories && !is_wp_error($categories) ? $categories[0]->name : 'Tool';
        
        return "{$tool_title} - {$category_name} Review & Guide | " . get_bloginfo('name');
    }
    
    if (is_tax('tool_category')) {
        $term = get_queried_object();
        return "Best {$term->name} Tools - Reviews & Comparisons | " . get_bloginfo('name');
    }
    
    if (is_tax('review_type')) {
        $term = get_queried_object();
        return "{$term->name} - Tool Reviews & Guides | " . get_bloginfo('name');
    }
    
    return $title;
}
add_filter('pre_get_document_title', 'toolspick_custom_meta_title');

/**
 * Add custom meta descriptions
 */
function toolspick_add_meta_description() {
    $description = '';
    
    if (is_singular('tool')) {
        $custom_desc = get_field('meta_description');
        if ($custom_desc) {
            $description = $custom_desc;
        } else {
            $short_desc = get_field('short_description');
            $description = $short_desc ?: wp_trim_words(get_the_content(), 25);
        }
    } elseif (is_tax('tool_category')) {
        $term = get_queried_object();
        $description = $term->description ?: "Discover the best {$term->name} tools. Compare features, pricing, and reviews to find the perfect solution for your needs.";
    } elseif (is_tax('review_type')) {
        $term = get_queried_object();
        $description = $term->description ?: "Browse {$term->name} for various tools. Get insights, comparisons, and detailed guides.";
    } elseif (is_post_type_archive('tool')) {
        $description = 'Comprehensive directory of AI and SaaS tools with detailed reviews, alternatives, pricing guides, and user ratings.';
    } elseif (is_front_page()) {
        $description = get_bloginfo('description') ?: 'Discover the best AI and SaaS tools for your business. Compare features, read reviews, and find alternatives.';
    }
    
    if ($description) {
        echo '<meta name="description" content="' . esc_attr(wp_trim_words($description, 25)) . '">' . "\n";
    }
}
add_action('wp_head', 'toolspick_add_meta_description', 1);

/**
 * Add Open Graph meta tags
 */
function toolspick_add_og_meta_tags() {
    $og_title = '';
    $og_description = '';
    $og_image = '';
    $og_url = '';
    $og_type = 'website';
    
    if (is_singular('tool')) {
        $og_title = get_the_title() . ' - Tool Review | ' . get_bloginfo('name');
        $og_description = get_field('short_description') ?: wp_trim_words(get_the_content(), 25);
        $og_image = get_the_post_thumbnail_url(get_the_ID(), 'large');
        $og_url = get_permalink();
        $og_type = 'article';
    } elseif (is_tax()) {
        $term = get_queried_object();
        $og_title = $term->name . ' Tools | ' . get_bloginfo('name');
        $og_description = $term->description ?: "Browse {$term->name} tools and reviews.";
        $og_url = get_term_link($term);
    } else {
        $og_title = get_bloginfo('name');
        $og_description = get_bloginfo('description');
        $og_url = home_url();
    }
    
    // Default image if none set
    if (!$og_image) {
        $og_image = get_site_icon_url(1200) ?: (get_template_directory_uri() . '/assets/images/og-default.png');
    }
    
    echo '<meta property="og:title" content="' . esc_attr($og_title) . '">' . "\n";
    echo '<meta property="og:description" content="' . esc_attr($og_description) . '">' . "\n";
    echo '<meta property="og:image" content="' . esc_url($og_image) . '">' . "\n";
    echo '<meta property="og:url" content="' . esc_url($og_url) . '">' . "\n";
    echo '<meta property="og:type" content="' . esc_attr($og_type) . '">' . "\n";
    echo '<meta property="og:site_name" content="' . esc_attr(get_bloginfo('name')) . '">' . "\n";
    
    // Twitter Card
    echo '<meta name="twitter:card" content="summary_large_image">' . "\n";
    echo '<meta name="twitter:title" content="' . esc_attr($og_title) . '">' . "\n";
    echo '<meta name="twitter:description" content="' . esc_attr($og_description) . '">' . "\n";
    echo '<meta name="twitter:image" content="' . esc_url($og_image) . '">' . "\n";
}
add_action('wp_head', 'toolspick_add_og_meta_tags', 2);

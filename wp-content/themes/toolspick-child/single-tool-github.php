<?php
/**
 * Single Tool Template - GitHub Style Layout
 * 
 * @package Toolspick
 * @since 1.0.0
 */

get_header();

// Get tool data
$tool_id = get_the_ID();
$short_description = get_field('short_description', $tool_id);
$official_website = get_field('official_website', $tool_id);
$pricing = get_field('pricing', $tool_id);
$pricing_details = get_field('pricing_details', $tool_id);
$features = get_field('features', $tool_id);
$supported_platforms = get_field('supported_platforms', $tool_id);
$integrations = get_field('integrations', $tool_id);
$alternatives = get_field('alternatives', $tool_id);
$rating = get_field('review_rating', $tool_id) ?: 4.0;
$video_tutorial = get_field('video_tutorial', $tool_id);
$affiliate_link = get_field('affiliate_link', $tool_id);
$votes = get_post_meta($tool_id, 'tool_votes', true) ?: 0;
$categories = get_the_terms($tool_id, 'tool_category');
$tags = get_the_terms($tool_id, 'tool_tag');
$gallery_images = get_field('tool_gallery', $tool_id);
$use_cases = get_field('use_cases', $tool_id);
?>

<main id="main" class="site-main single-tool-github">
    
    <!-- Tool Header -->
    <section class="tool-header-github">
        <div class="container">
            <div class="tool-header-content">
                <div class="tool-title-section">
                    <?php if (has_post_thumbnail($tool_id)) : ?>
                        <img src="<?php echo esc_url(get_the_post_thumbnail_url($tool_id, 'thumbnail')); ?>" 
                             alt="<?php echo esc_attr(get_the_title()); ?> logo" 
                             class="tool-logo">
                    <?php endif; ?>
                    
                    <div class="tool-title-info">
                        <h1 class="tool-title"><?php the_title(); ?></h1>
                        <?php if ($short_description) : ?>
                            <p class="tool-description"><?php echo esc_html($short_description); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="tool-actions-github">
                    <div class="action-buttons">
                        <button class="btn-github btn-claim">
                            <span class="btn-icon">🏷️</span>
                            Claim
                        </button>
                        
                        <?php
                        $primary_link = $affiliate_link ?: $official_website;
                        if ($primary_link) :
                            ?>
                            <a href="<?php echo esc_url($primary_link); ?>" 
                               target="_blank" 
                               rel="noopener <?php echo $affiliate_link ? 'sponsored' : 'nofollow'; ?>"
                               class="btn-github btn-visit">
                                <span class="btn-icon">🔗</span>
                                Visit
                            </a>
                        <?php endif; ?>
                        
                        <button class="btn-github btn-share" onclick="shareUrl()">
                            <span class="btn-icon">📤</span>
                            Share
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Main Content Layout -->
    <section class="tool-content-github">
        <div class="container">
            <div class="content-layout-github">
                
                <!-- Main Content -->
                <div class="main-content-github">
                    
                    <!-- Tool Description -->
                    <div class="tool-description-section">
                        <?php the_content(); ?>
                    </div>
                    
                    <!-- Screenshots/Gallery -->
                    <?php if ($gallery_images && is_array($gallery_images)) : ?>
                        <div class="tool-gallery-github">
                            <div class="gallery-container">
                                <?php foreach ($gallery_images as $index => $image) : ?>
                                    <div class="gallery-item" onclick="openLightbox(<?php echo $index; ?>)">
                                        <img src="<?php echo esc_url($image['sizes']['large']); ?>" 
                                             alt="<?php echo esc_attr($image['alt']); ?>"
                                             loading="lazy">
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Features Section -->
                    <?php if ($features && is_array($features)) : ?>
                        <div class="features-section-github">
                            <div class="features-list">
                                <?php foreach ($features as $feature) : ?>
                                    <div class="feature-item">
                                        <span class="feature-bullet">•</span>
                                        <span class="feature-text"><?php echo esc_html($feature['feature_text']); ?></span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Categories and Tags -->
                    <div class="taxonomy-section-github">
                        <?php if ($categories && !is_wp_error($categories)) : ?>
                            <div class="categories-section">
                                <h4>Categories:</h4>
                                <div class="taxonomy-tags">
                                    <?php foreach ($categories as $category) : ?>
                                        <a href="<?php echo esc_url(get_term_link($category)); ?>" 
                                           class="taxonomy-tag">
                                            <?php echo esc_html($category->name); ?>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($tags && !is_wp_error($tags)) : ?>
                            <div class="tags-section">
                                <h4>Tags:</h4>
                                <div class="taxonomy-tags">
                                    <?php foreach ($tags as $tag) : ?>
                                        <a href="<?php echo esc_url(get_term_link($tag)); ?>" 
                                           class="taxonomy-tag tag-style">
                                            <?php echo esc_html($tag->name); ?>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                </div>
                
                <!-- Sidebar -->
                <div class="sidebar-github">
                    
                    <!-- Details Card -->
                    <div class="details-card">
                        <h3>Details:</h3>
                        
                        <div class="detail-item">
                            <span class="detail-icon">⭐</span>
                            <span class="detail-label">Rating</span>
                            <span class="detail-value"><?php echo number_format($rating, 1); ?></span>
                        </div>
                        
                        <div class="detail-item">
                            <span class="detail-icon">👍</span>
                            <span class="detail-label">Votes</span>
                            <span class="detail-value"><?php echo number_format($votes); ?></span>
                        </div>
                        
                        <?php if ($pricing) : ?>
                            <div class="detail-item">
                                <span class="detail-icon">💰</span>
                                <span class="detail-label">Pricing</span>
                                <span class="detail-value pricing-badge"><?php echo esc_html($pricing); ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($supported_platforms && is_array($supported_platforms)) : ?>
                            <div class="detail-item">
                                <span class="detail-icon">💻</span>
                                <span class="detail-label">Platforms</span>
                                <div class="detail-value">
                                    <?php foreach ($supported_platforms as $platform) : ?>
                                        <span class="platform-badge"><?php echo esc_html($platform); ?></span>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <div class="detail-item">
                            <span class="detail-icon">📅</span>
                            <span class="detail-label">Added</span>
                            <span class="detail-value"><?php echo get_the_date('M j, Y'); ?></span>
                        </div>
                        
                        <?php if ($official_website) : ?>
                            <div class="detail-item">
                                <a href="<?php echo esc_url($official_website); ?>" 
                                   target="_blank" 
                                   rel="noopener nofollow"
                                   class="btn-github btn-full-width">
                                    <span class="btn-icon">🌐</span>
                                    View Repository
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                </div>
                
            </div>
        </div>
    </section>
    
</main>

<?php get_footer(); ?>

<?php
/**
 * Tool Category Archive Template
 * 
 * @package Toolspick
 * @since 1.0.0
 */

get_header();

$term = get_queried_object();
$category_icon = toolspick_get_category_icon($term->term_id);
$category_color = toolspick_get_category_color($term->term_id);
?>

<main id="main" class="site-main category-archive">
    
    <!-- Category Header -->
    <section class="category-header" style="background: linear-gradient(135deg, <?php echo esc_attr($category_color); ?>, <?php echo esc_attr($category_color); ?>dd);">
        <div class="container">
            <div class="category-header-content">
                <?php if ($category_icon) : ?>
                    <span class="category-icon-large"><?php echo esc_html($category_icon); ?></span>
                <?php endif; ?>
                
                <div class="category-header-info">
                    <h1 class="category-title"><?php echo esc_html($term->name); ?> Tools</h1>
                    
                    <?php if ($term->description) : ?>
                        <p class="category-description"><?php echo esc_html($term->description); ?></p>
                    <?php endif; ?>
                    
                    <div class="category-stats">
                        <span class="stat-item">
                            <strong><?php echo number_format($term->count); ?></strong> 
                            <?php printf(_n('Tool', 'Tools', $term->count, 'toolspick'), $term->count); ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Breadcrumbs -->
    <section class="breadcrumbs-section">
        <div class="container">
            <?php echo toolspick_get_breadcrumbs(); ?>
        </div>
    </section>

    <!-- Search and Filters -->
    <section class="category-filters">
        <div class="container">
            <div class="filters-wrapper">
                
                <!-- Search Form -->
                <form class="category-search-form" method="get">
                    <input type="search" 
                           name="s" 
                           placeholder="Search in <?php echo esc_attr($term->name); ?>..." 
                           value="<?php echo esc_attr(get_search_query()); ?>"
                           class="search-input">
                    <input type="hidden" name="post_type" value="tool">
                    <input type="hidden" name="tool_category" value="<?php echo esc_attr($term->slug); ?>">
                    <button type="submit" class="search-button">
                        <span class="dashicons dashicons-search"></span>
                    </button>
                </form>

                <!-- Sort Options -->
                <div class="sort-options">
                    <label for="sort-by">Sort by:</label>
                    <select id="sort-by" name="orderby" class="sort-select" onchange="this.form.submit()">
                        <option value="votes" <?php selected(get_query_var('orderby'), 'votes'); ?>>Most Voted</option>
                        <option value="date" <?php selected(get_query_var('orderby'), 'date'); ?>>Newest</option>
                        <option value="title" <?php selected(get_query_var('orderby'), 'title'); ?>>Name A-Z</option>
                        <option value="rating" <?php selected(get_query_var('orderby'), 'rating'); ?>>Highest Rated</option>
                    </select>
                </div>

                <!-- View Toggle -->
                <div class="view-toggle">
                    <button class="view-btn active" data-view="grid" title="Grid View">
                        <span class="dashicons dashicons-grid-view"></span>
                    </button>
                    <button class="view-btn" data-view="list" title="List View">
                        <span class="dashicons dashicons-list-view"></span>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Tools Grid -->
    <section class="category-tools-section">
        <div class="container">
            
            <?php if (have_posts()) : ?>
                
                <div class="category-tools-header">
                    <h2>
                        <?php
                        if (is_search()) {
                            printf('Search Results in %s', $term->name);
                        } else {
                            printf('All %s Tools', $term->name);
                        }
                        ?>
                    </h2>
                    <div class="results-info">
                        <?php
                        global $wp_query;
                        $total = $wp_query->found_posts;
                        $current_page = max(1, get_query_var('paged'));
                        $per_page = get_query_var('posts_per_page');
                        $start = (($current_page - 1) * $per_page) + 1;
                        $end = min($current_page * $per_page, $total);
                        
                        printf('Showing %d-%d of %d tools', $start, $end, $total);
                        ?>
                    </div>
                </div>

                <div class="tool-grid grid-view" id="tools-container">
                    <?php while (have_posts()) : the_post(); ?>
                        <?php include(get_stylesheet_directory() . '/templates/parts/tool-card.php'); ?>
                    <?php endwhile; ?>
                </div>

                <!-- Pagination -->
                <div class="pagination-wrapper">
                    <?php
                    echo paginate_links([
                        'prev_text' => '<span class="dashicons dashicons-arrow-left-alt2"></span> Previous',
                        'next_text' => 'Next <span class="dashicons dashicons-arrow-right-alt2"></span>',
                        'type' => 'list',
                        'class' => 'pagination'
                    ]);
                    ?>
                </div>

            <?php else : ?>
                
                <div class="no-tools-found">
                    <div class="no-tools-content">
                        <span class="no-tools-icon">🔍</span>
                        <h2>No tools found</h2>
                        <?php if (is_search()) : ?>
                            <p>We couldn't find any <?php echo esc_html($term->name); ?> tools matching your search.</p>
                        <?php else : ?>
                            <p>No tools have been added to this category yet.</p>
                        <?php endif; ?>
                        
                        <div class="no-tools-actions">
                            <a href="<?php echo esc_url(get_post_type_archive_link('tool')); ?>" class="btn-primary">
                                Browse All Tools
                            </a>
                        </div>
                    </div>
                </div>

            <?php endif; ?>
        </div>
    </section>

    <!-- Related Categories -->
    <?php
    $related_categories = get_terms([
        'taxonomy' => 'tool_category',
        'hide_empty' => true,
        'exclude' => [$term->term_id],
        'number' => 6,
        'orderby' => 'count',
        'order' => 'DESC'
    ]);
    
    if ($related_categories && !is_wp_error($related_categories)) :
        ?>
        <section class="related-categories-section">
            <div class="container">
                <h2>Related Categories</h2>
                <div class="related-categories-grid">
                    <?php foreach ($related_categories as $related_category) : ?>
                        <?php
                        $related_icon = toolspick_get_category_icon($related_category->term_id);
                        $related_color = toolspick_get_category_color($related_category->term_id);
                        ?>
                        <a href="<?php echo esc_url(get_term_link($related_category)); ?>" 
                           class="related-category-card"
                           style="border-top: 4px solid <?php echo esc_attr($related_color); ?>">
                            <span class="category-icon"><?php echo esc_html($related_icon); ?></span>
                            <h3 class="category-name"><?php echo esc_html($related_category->name); ?></h3>
                            <p class="category-count">
                                <?php printf(_n('%d tool', '%d tools', $related_category->count, 'toolspick'), $related_category->count); ?>
                            </p>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
    <?php endif; ?>

</main>

<script>
// View toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const viewButtons = document.querySelectorAll('.view-btn');
    const toolsContainer = document.getElementById('tools-container');
    
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            viewButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            
            // Update container class
            const view = this.dataset.view;
            toolsContainer.className = `tool-grid ${view}-view`;
        });
    });
});
</script>

<style>
/* Category Archive Specific Styles */
.category-header {
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
}

.category-header-content {
    display: flex;
    align-items: center;
    gap: 2rem;
    text-align: left;
}

.category-icon-large {
    font-size: 4rem;
    line-height: 1;
    flex-shrink: 0;
}

.category-title {
    margin: 0 0 1rem 0;
    font-size: 2.5rem;
    font-weight: 700;
}

.category-description {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: 0 0 1.5rem 0;
}

.category-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    font-size: 0.875rem;
    opacity: 0.9;
}

.breadcrumbs-section {
    background: #f9fafb;
    padding: 1rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.category-filters {
    background: #f9fafb;
    padding: 1.5rem 0;
    margin-bottom: 2rem;
}

.filters-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    align-items: center;
}

.category-search-form {
    display: flex;
    flex: 1;
    max-width: 400px;
}

.search-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px 0 0 8px;
    font-size: 0.875rem;
}

.search-button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 0 8px 8px 0;
    cursor: pointer;
}

.sort-options {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sort-select {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
}

.view-toggle {
    display: flex;
    gap: 0.25rem;
    background: white;
    border-radius: 6px;
    padding: 0.25rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.view-btn {
    background: transparent;
    border: none;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    color: #6b7280;
    transition: all 0.2s ease;
}

.view-btn.active,
.view-btn:hover {
    background: var(--primary-color);
    color: white;
}

.category-tools-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.results-info {
    color: #6b7280;
    font-size: 0.875rem;
}

.tool-grid.list-view {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.tool-grid.list-view .tool-card {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
}

.tool-grid.list-view .tool-card .tool-logo {
    width: 60px;
    height: 60px;
    flex-shrink: 0;
}

.tool-grid.list-view .tool-card .tool-info {
    flex: 1;
}

.no-tools-found {
    text-align: center;
    padding: 4rem 0;
}

.no-tools-icon {
    font-size: 4rem;
    display: block;
    margin-bottom: 1rem;
}

.no-tools-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.related-categories-section {
    background: #f9fafb;
    padding: 3rem 0;
    margin-top: 3rem;
}

.related-categories-section h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--primary-color);
    font-size: 1.75rem;
    font-weight: 600;
}

.related-categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.related-category-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    text-align: center;
}

.related-category-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    text-decoration: none;
    color: inherit;
}

.related-category-card .category-icon {
    font-size: 2.5rem;
    display: block;
    margin-bottom: 1rem;
}

.related-category-card .category-name {
    margin: 0 0 0.5rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--primary-color);
}

.related-category-card .category-count {
    margin: 0;
    color: #6b7280;
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    .category-header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .category-title {
        font-size: 2rem;
    }
    
    .filters-wrapper {
        flex-direction: column;
        align-items: stretch;
    }
    
    .category-search-form {
        max-width: none;
    }
    
    .category-tools-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .related-categories-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php get_footer(); ?>

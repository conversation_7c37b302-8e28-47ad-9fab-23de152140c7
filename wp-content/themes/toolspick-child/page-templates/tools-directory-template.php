<?php
/**
 * Template Name: Tools Directory
 * 
 * @package Toolspick
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main tools-directory">
    
    <!-- Directory Header -->
    <section class="directory-header">
        <div class="container">
            <div class="directory-header-content">
                <h1 class="directory-title">
                    <?php echo get_field('directory_title') ?: 'All Tools'; ?>
                </h1>
                <p class="directory-description">
                    <?php echo get_field('directory_description') ?: 'Discover the best AI and SaaS tools for your business. Compare features, read reviews, and find the perfect solution.'; ?>
                </p>
                
                <div class="directory-stats">
                    <?php
                    $tools_count = wp_count_posts('tool');
                    $categories_count = wp_count_terms(['taxonomy' => 'tool_category', 'hide_empty' => true]);
                    ?>
                    <span class="stat-item">
                        <strong><?php echo number_format($tools_count->publish); ?></strong> Tools
                    </span>
                    <span class="stat-item">
                        <strong><?php echo number_format($categories_count); ?></strong> Categories
                    </span>
                </div>
            </div>
        </div>
    </section>

    <!-- Search and Filters -->
    <section class="tools-filters">
        <div class="container">
            <div class="filters-wrapper">
                
                <!-- Search Form -->
                <form class="tools-search-form" method="get" action="<?php echo esc_url(get_permalink()); ?>">
                    <input type="search" 
                           name="tool_search" 
                           placeholder="Search tools..." 
                           value="<?php echo esc_attr($_GET['tool_search'] ?? ''); ?>"
                           class="search-input">
                    <button type="submit" class="search-button">
                        <span class="dashicons dashicons-search"></span>
                    </button>
                </form>

                <!-- Category Filter -->
                <div class="category-filter">
                    <label for="category-filter">Category:</label>
                    <select id="category-filter" name="tool_category" class="category-select" onchange="filterTools()">
                        <option value="">All Categories</option>
                        <?php
                        $categories = get_terms([
                            'taxonomy' => 'tool_category',
                            'hide_empty' => true,
                            'orderby' => 'name'
                        ]);
                        
                        $selected_category = $_GET['tool_category'] ?? '';
                        foreach ($categories as $category) :
                            ?>
                            <option value="<?php echo esc_attr($category->slug); ?>" 
                                    <?php selected($selected_category, $category->slug); ?>>
                                <?php echo esc_html($category->name); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Pricing Filter -->
                <div class="pricing-filter">
                    <label>Pricing:</label>
                    <div class="pricing-checkboxes">
                        <?php
                        $selected_pricing = $_GET['pricing'] ?? [];
                        if (!is_array($selected_pricing)) {
                            $selected_pricing = explode(',', $selected_pricing);
                        }
                        ?>
                        <label class="checkbox-label">
                            <input type="checkbox" name="pricing[]" value="free" 
                                   <?php checked(in_array('free', $selected_pricing)); ?>>
                            Free
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" name="pricing[]" value="freemium" 
                                   <?php checked(in_array('freemium', $selected_pricing)); ?>>
                            Freemium
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" name="pricing[]" value="paid" 
                                   <?php checked(in_array('paid', $selected_pricing)); ?>>
                            Paid
                        </label>
                    </div>
                </div>

                <!-- Sort Options -->
                <div class="sort-options">
                    <label for="sort-by">Sort by:</label>
                    <select id="sort-by" name="sort" class="sort-select" onchange="filterTools()">
                        <?php $selected_sort = $_GET['sort'] ?? 'votes'; ?>
                        <option value="votes" <?php selected($selected_sort, 'votes'); ?>>Most Voted</option>
                        <option value="date" <?php selected($selected_sort, 'date'); ?>>Newest</option>
                        <option value="title" <?php selected($selected_sort, 'title'); ?>>Name A-Z</option>
                        <option value="rating" <?php selected($selected_sort, 'rating'); ?>>Highest Rated</option>
                    </select>
                </div>

                <!-- Clear Filters -->
                <button type="button" class="clear-filters-btn" onclick="clearFilters()">
                    Clear Filters
                </button>
            </div>
        </div>
    </section>

    <!-- Featured Categories (if enabled) -->
    <?php if (get_field('show_categories_section')) : ?>
    <section class="featured-categories-section">
        <div class="container">
            <h2><?php echo get_field('categories_section_title') ?: 'Browse by Category'; ?></h2>
            <div class="categories-grid">
                <?php
                $categories_to_show = get_field('categories_to_show') ?: 8;
                $featured_categories = toolspick_get_featured_categories($categories_to_show);
                
                if ($featured_categories) :
                    foreach ($featured_categories as $category) :
                        $icon = toolspick_get_category_icon($category->term_id);
                        $color = toolspick_get_category_color($category->term_id);
                        ?>
                        <a href="javascript:void(0)" 
                           class="category-card"
                           style="border-top: 4px solid <?php echo esc_attr($color); ?>"
                           onclick="filterByCategory('<?php echo esc_js($category->slug); ?>')">
                            <span class="category-icon"><?php echo esc_html($icon); ?></span>
                            <h3 class="category-name"><?php echo esc_html($category->name); ?></h3>
                            <p class="category-count">
                                <?php printf(_n('%d tool', '%d tools', $category->count, 'toolspick'), $category->count); ?>
                            </p>
                        </a>
                        <?php
                    endforeach;
                endif;
                ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Tools Grid -->
    <section class="tools-grid-section">
        <div class="container">
            
            <div class="tools-grid-header">
                <h2 id="results-title">All Tools</h2>
                <div class="results-info" id="results-info">
                    Loading...
                </div>
            </div>

            <div class="tool-grid" id="tools-container">
                <!-- Tools will be loaded here via AJAX -->
            </div>

            <!-- Load More Button -->
            <div class="load-more-wrapper text-center">
                <button id="load-more-tools" class="btn-primary" style="display: none;">
                    Load More Tools
                </button>
            </div>

            <!-- Loading Indicator -->
            <div class="loading-indicator" id="loading-indicator">
                <div class="spinner"></div>
                <p>Loading tools...</p>
            </div>

            <!-- No Results -->
            <div class="no-tools-found" id="no-results" style="display: none;">
                <div class="no-tools-content">
                    <span class="no-tools-icon">🔍</span>
                    <h3>No tools found</h3>
                    <p>Try adjusting your search terms or filters.</p>
                    <button class="btn-primary" onclick="clearFilters()">
                        Clear All Filters
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Custom Content -->
    <?php if (have_posts()) : while (have_posts()) : the_post(); ?>
        <?php if (get_the_content()) : ?>
            <section class="custom-content-section">
                <div class="container">
                    <div class="custom-content">
                        <?php the_content(); ?>
                    </div>
                </div>
            </section>
        <?php endif; ?>
    <?php endwhile; endif; ?>

</main>

<script>
// Tools Directory JavaScript
let currentPage = 1;
let isLoading = false;
let hasMoreTools = true;

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    loadTools();
    
    // Add event listeners
    document.querySelector('.tools-search-form').addEventListener('submit', function(e) {
        e.preventDefault();
        resetAndLoadTools();
    });
    
    document.querySelectorAll('.pricing-checkboxes input').forEach(function(checkbox) {
        checkbox.addEventListener('change', resetAndLoadTools);
    });
    
    document.getElementById('load-more-tools').addEventListener('click', function() {
        currentPage++;
        loadTools(false);
    });
});

function resetAndLoadTools() {
    currentPage = 1;
    hasMoreTools = true;
    document.getElementById('tools-container').innerHTML = '';
    loadTools();
}

function loadTools(reset = true) {
    if (isLoading) return;
    
    isLoading = true;
    document.getElementById('loading-indicator').style.display = 'block';
    
    const formData = new FormData();
    formData.append('action', 'load_directory_tools');
    formData.append('page', currentPage);
    formData.append('search', document.querySelector('input[name="tool_search"]').value);
    formData.append('category', document.querySelector('select[name="tool_category"]').value);
    formData.append('sort', document.querySelector('select[name="sort"]').value);
    
    // Get pricing filters
    const pricingFilters = [];
    document.querySelectorAll('.pricing-checkboxes input:checked').forEach(function(checkbox) {
        pricingFilters.push(checkbox.value);
    });
    formData.append('pricing', pricingFilters.join(','));
    
    fetch(ajaxurl || '/wp-admin/admin-ajax.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('loading-indicator').style.display = 'none';
        
        if (data.success) {
            if (reset || currentPage === 1) {
                document.getElementById('tools-container').innerHTML = data.data.html;
            } else {
                document.getElementById('tools-container').innerHTML += data.data.html;
            }
            
            // Update results info
            document.getElementById('results-info').textContent = data.data.results_text;
            document.getElementById('results-title').textContent = data.data.title;
            
            // Show/hide load more button
            if (data.data.has_more) {
                document.getElementById('load-more-tools').style.display = 'block';
            } else {
                document.getElementById('load-more-tools').style.display = 'none';
            }
            
            // Show/hide no results
            if (data.data.total === 0) {
                document.getElementById('no-results').style.display = 'block';
            } else {
                document.getElementById('no-results').style.display = 'none';
            }
        } else {
            console.error('Error loading tools:', data.data);
        }
        
        isLoading = false;
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('loading-indicator').style.display = 'none';
        isLoading = false;
    });
}

function filterTools() {
    resetAndLoadTools();
}

function filterByCategory(categorySlug) {
    document.querySelector('select[name="tool_category"]').value = categorySlug;
    resetAndLoadTools();
}

function clearFilters() {
    document.querySelector('input[name="tool_search"]').value = '';
    document.querySelector('select[name="tool_category"]').value = '';
    document.querySelector('select[name="sort"]').value = 'votes';
    document.querySelectorAll('.pricing-checkboxes input').forEach(function(checkbox) {
        checkbox.checked = false;
    });
    resetAndLoadTools();
}
</script>

<style>
/* Tools Directory Specific Styles */
.directory-header {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
    text-align: center;
}

.directory-title {
    margin: 0 0 1rem 0;
    font-size: 2.5rem;
    font-weight: 700;
}

.directory-description {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: 0 0 2rem 0;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.directory-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.stat-item {
    font-size: 0.875rem;
    opacity: 0.9;
}

.tools-filters {
    background: #f9fafb;
    padding: 1.5rem 0;
    margin-bottom: 2rem;
}

.filters-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    align-items: center;
}

.tools-search-form {
    display: flex;
    flex: 1;
    max-width: 400px;
}

.search-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px 0 0 8px;
    font-size: 0.875rem;
}

.search-button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 0 8px 8px 0;
    cursor: pointer;
}

.category-filter, .sort-options, .pricing-filter {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.category-select, .sort-select {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
}

.pricing-checkboxes {
    display: flex;
    gap: 1rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
}

.clear-filters-btn {
    background: #ef4444;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
}

.clear-filters-btn:hover {
    background: #dc2626;
}

.tools-grid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.results-info {
    color: #6b7280;
    font-size: 0.875rem;
}

.loading-indicator {
    text-align: center;
    padding: 2rem;
}

.spinner {
    border: 3px solid #f3f4f6;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.no-tools-found {
    text-align: center;
    padding: 4rem 0;
}

.no-tools-icon {
    font-size: 4rem;
    display: block;
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .directory-title {
        font-size: 2rem;
    }
    
    .directory-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .filters-wrapper {
        flex-direction: column;
        align-items: stretch;
    }
    
    .tools-search-form {
        max-width: none;
    }
    
    .tools-grid-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}
</style>

<?php get_footer(); ?>

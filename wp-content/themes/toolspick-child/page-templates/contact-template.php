<?php
/**
 * Template Name: Contact Page
 * 
 * @package Toolspick
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main contact-page">
    
    <!-- Contact Header -->
    <section class="contact-header">
        <div class="container">
            <div class="contact-header-content">
                <h1 class="contact-title">
                    <?php echo get_field('contact_title') ?: 'Get in Touch'; ?>
                </h1>
                <p class="contact-description">
                    <?php echo get_field('contact_description') ?: 'Have a question about our tools directory? Want to submit a tool? We\'d love to hear from you.'; ?>
                </p>
            </div>
        </div>
    </section>

    <!-- Contact Content -->
    <section class="contact-content-section">
        <div class="container">
            <div class="contact-content">
                
                <!-- Contact Form -->
                <div class="contact-form-wrapper">
                    <h2><?php echo get_field('form_title') ?: 'Send us a Message'; ?></h2>
                    
                    <form class="contact-form" id="contact-form" method="post">
                        <?php wp_nonce_field('toolspick_contact_form', 'contact_nonce'); ?>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="contact_name">Name *</label>
                                <input type="text" id="contact_name" name="contact_name" required>
                            </div>
                            <div class="form-group">
                                <label for="contact_email">Email *</label>
                                <input type="email" id="contact_email" name="contact_email" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="contact_subject">Subject *</label>
                            <select id="contact_subject" name="contact_subject" required>
                                <option value="">Select a subject</option>
                                <option value="general">General Inquiry</option>
                                <option value="tool_submission">Submit a Tool</option>
                                <option value="tool_update">Update Tool Information</option>
                                <option value="partnership">Partnership Opportunity</option>
                                <option value="bug_report">Report a Bug</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="contact_message">Message *</label>
                            <textarea id="contact_message" name="contact_message" rows="6" required placeholder="Tell us more about your inquiry..."></textarea>
                        </div>
                        
                        <?php if (get_field('show_newsletter_signup')) : ?>
                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="subscribe_newsletter" value="1">
                                <span class="checkmark"></span>
                                Subscribe to our newsletter for tool updates
                            </label>
                        </div>
                        <?php endif; ?>
                        
                        <div class="form-group">
                            <button type="submit" class="btn-primary btn-large">
                                <span class="button-text">Send Message</span>
                                <span class="button-loading" style="display: none;">Sending...</span>
                            </button>
                        </div>
                    </form>
                    
                    <div class="form-messages" id="form-messages"></div>
                </div>

                <!-- Contact Info -->
                <div class="contact-info-wrapper">
                    <h2><?php echo get_field('info_title') ?: 'Contact Information'; ?></h2>
                    
                    <div class="contact-info-cards">
                        
                        <?php if (get_field('show_email_info')) : ?>
                        <div class="contact-info-card">
                            <div class="contact-icon">
                                <span class="dashicons dashicons-email-alt"></span>
                            </div>
                            <div class="contact-details">
                                <h3>Email Us</h3>
                                <p>
                                    <a href="mailto:<?php echo esc_attr(get_field('contact_email') ?: '<EMAIL>'); ?>">
                                        <?php echo esc_html(get_field('contact_email') ?: '<EMAIL>'); ?>
                                    </a>
                                </p>
                                <small>We'll respond within 24 hours</small>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (get_field('show_social_info')) : ?>
                        <div class="contact-info-card">
                            <div class="contact-icon">
                                <span class="dashicons dashicons-share"></span>
                            </div>
                            <div class="contact-details">
                                <h3>Follow Us</h3>
                                <div class="social-links">
                                    <?php if (get_field('twitter_url')) : ?>
                                        <a href="<?php echo esc_url(get_field('twitter_url')); ?>" target="_blank" rel="noopener">Twitter</a>
                                    <?php endif; ?>
                                    <?php if (get_field('linkedin_url')) : ?>
                                        <a href="<?php echo esc_url(get_field('linkedin_url')); ?>" target="_blank" rel="noopener">LinkedIn</a>
                                    <?php endif; ?>
                                </div>
                                <small>Stay updated with the latest tools</small>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (get_field('show_response_info')) : ?>
                        <div class="contact-info-card">
                            <div class="contact-icon">
                                <span class="dashicons dashicons-clock"></span>
                            </div>
                            <div class="contact-details">
                                <h3>Response Time</h3>
                                <p><?php echo esc_html(get_field('response_time') ?: '24 hours'); ?></p>
                                <small>Average response time for inquiries</small>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                    </div>
                    
                    <!-- FAQ Section -->
                    <?php if (get_field('show_faq_section')) : ?>
                    <div class="contact-faq">
                        <h3>Frequently Asked Questions</h3>
                        <div class="faq-items">
                            <div class="faq-item">
                                <h4>How do I submit a tool for review?</h4>
                                <p>Use the contact form above with "Submit a Tool" as the subject, and include the tool's website URL and a brief description.</p>
                            </div>
                            <div class="faq-item">
                                <h4>Can I update information about my tool?</h4>
                                <p>Yes! Contact us with "Update Tool Information" and we'll help you keep your tool's listing current.</p>
                            </div>
                            <div class="faq-item">
                                <h4>Do you offer partnership opportunities?</h4>
                                <p>We're always open to partnerships. Select "Partnership Opportunity" and tell us about your proposal.</p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Custom Content -->
    <?php if (have_posts()) : while (have_posts()) : the_post(); ?>
        <?php if (get_the_content()) : ?>
            <section class="custom-content-section">
                <div class="container">
                    <div class="custom-content">
                        <?php the_content(); ?>
                    </div>
                </div>
            </section>
        <?php endif; ?>
    <?php endwhile; endif; ?>

</main>

<script>
// Contact Form JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contact-form');
    const formMessages = document.getElementById('form-messages');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitButton = contactForm.querySelector('button[type="submit"]');
            const buttonText = submitButton.querySelector('.button-text');
            const buttonLoading = submitButton.querySelector('.button-loading');
            
            // Show loading state
            submitButton.disabled = true;
            buttonText.style.display = 'none';
            buttonLoading.style.display = 'inline';
            
            // Clear previous messages
            formMessages.innerHTML = '';
            
            // Prepare form data
            const formData = new FormData(contactForm);
            formData.append('action', 'toolspick_contact_form');
            
            // Submit form
            fetch(ajaxurl || '/wp-admin/admin-ajax.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('Thank you! Your message has been sent successfully.', 'success');
                    contactForm.reset();
                } else {
                    showMessage(data.data.message || 'Something went wrong. Please try again.', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('Something went wrong. Please try again.', 'error');
            })
            .finally(() => {
                // Reset button state
                submitButton.disabled = false;
                buttonText.style.display = 'inline';
                buttonLoading.style.display = 'none';
            });
        });
    }
    
    function showMessage(message, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `form-message form-message-${type}`;
        messageDiv.textContent = message;
        formMessages.appendChild(messageDiv);
        
        // Scroll to message
        messageDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
});
</script>

<style>
/* Contact Page Specific Styles */
.contact-header {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.contact-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 1rem 0;
}

.contact-description {
    font-size: 1.125rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.contact-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    margin: 3rem 0;
}

.contact-form-wrapper h2,
.contact-info-wrapper h2 {
    color: var(--primary-color);
    margin-bottom: 2rem;
    font-size: 1.5rem;
    font-weight: 600;
}

.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.checkbox-group {
    margin: 1.5rem 0;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
}

.contact-info-cards {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-info-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: var(--shadow);
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.contact-icon {
    background: var(--primary-color);
    color: white;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-details h3 {
    margin: 0 0 0.5rem 0;
    color: var(--primary-color);
    font-size: 1.125rem;
    font-weight: 600;
}

.contact-details p {
    margin: 0 0 0.25rem 0;
}

.contact-details small {
    color: #6b7280;
    font-size: 0.875rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.social-links a:hover {
    text-decoration: underline;
}

.contact-faq {
    margin-top: 2rem;
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow);
}

.contact-faq h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.faq-items {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.faq-item h4 {
    color: #374151;
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
}

.faq-item p {
    color: #6b7280;
    margin: 0;
    line-height: 1.6;
}

.form-messages {
    margin-top: 1rem;
}

.form-message {
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
}

.form-message-success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #10b981;
}

.form-message-error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #ef4444;
}

@media (max-width: 768px) {
    .contact-title {
        font-size: 2rem;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .contact-form {
        padding: 1.5rem;
    }
}
</style>

<?php get_footer(); ?>

<?php
/**
 * Template Name: Category Showcase
 * 
 * @package Toolspick
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main category-showcase">
    
    <!-- Showcase Header -->
    <section class="showcase-header">
        <div class="container">
            <div class="showcase-header-content">
                <h1 class="showcase-title">
                    <?php echo get_field('showcase_title') ?: 'Tool Categories'; ?>
                </h1>
                <p class="showcase-description">
                    <?php echo get_field('showcase_description') ?: 'Explore our comprehensive collection of tool categories. Find the perfect tools for your specific needs.'; ?>
                </p>
            </div>
        </div>
    </section>

    <!-- Search Categories -->
    <section class="category-search-section">
        <div class="container">
            <div class="search-form-wrapper">
                <form class="category-search-form" method="get">
                    <input type="search" 
                           name="category_search" 
                           placeholder="Search categories..." 
                           value="<?php echo esc_attr($_GET['category_search'] ?? ''); ?>"
                           class="search-input">
                    <button type="submit" class="search-button">
                        <span class="dashicons dashicons-search"></span>
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- Featured Categories -->
    <?php if (get_field('show_featured_categories')) : ?>
    <section class="featured-categories-section">
        <div class="container">
            <div class="section-header">
                <h2><?php echo get_field('featured_section_title') ?: 'Featured Categories'; ?></h2>
                <p><?php echo get_field('featured_section_subtitle') ?: 'Most popular tool categories'; ?></p>
            </div>
            
            <div class="featured-categories-grid">
                <?php
                $featured_count = get_field('featured_categories_count') ?: 6;
                $featured_categories = toolspick_get_featured_categories($featured_count);
                
                if ($featured_categories) :
                    foreach ($featured_categories as $category) :
                        $icon = toolspick_get_category_icon($category->term_id);
                        $color = toolspick_get_category_color($category->term_id);
                        $tools_count = $category->count;
                        ?>
                        <a href="<?php echo esc_url(get_term_link($category)); ?>" 
                           class="featured-category-card"
                           style="background: linear-gradient(135deg, <?php echo esc_attr($color); ?>22, <?php echo esc_attr($color); ?>11);">
                            <div class="category-card-header">
                                <span class="category-icon-large" style="color: <?php echo esc_attr($color); ?>">
                                    <?php echo esc_html($icon); ?>
                                </span>
                                <div class="category-badge" style="background: <?php echo esc_attr($color); ?>">
                                    Featured
                                </div>
                            </div>
                            <div class="category-card-content">
                                <h3 class="category-name"><?php echo esc_html($category->name); ?></h3>
                                <p class="category-description"><?php echo esc_html($category->description); ?></p>
                                <div class="category-stats">
                                    <span class="tools-count">
                                        <?php printf(_n('%d tool', '%d tools', $tools_count, 'toolspick'), $tools_count); ?>
                                    </span>
                                </div>
                            </div>
                        </a>
                        <?php
                    endforeach;
                endif;
                ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- All Categories -->
    <section class="all-categories-section">
        <div class="container">
            <div class="section-header">
                <h2><?php echo get_field('all_categories_title') ?: 'All Categories'; ?></h2>
                <p><?php echo get_field('all_categories_subtitle') ?: 'Browse all available tool categories'; ?></p>
            </div>
            
            <!-- Category Filter Tabs -->
            <div class="category-filter-tabs">
                <button class="filter-tab active" data-filter="all">All</button>
                <button class="filter-tab" data-filter="popular">Most Popular</button>
                <button class="filter-tab" data-filter="recent">Recently Added</button>
                <button class="filter-tab" data-filter="alphabetical">A-Z</button>
            </div>
            
            <div class="categories-grid" id="categories-grid">
                <?php
                $search_term = $_GET['category_search'] ?? '';
                $categories_args = [
                    'taxonomy' => 'tool_category',
                    'hide_empty' => true,
                    'orderby' => 'count',
                    'order' => 'DESC'
                ];
                
                if ($search_term) {
                    $categories_args['search'] = $search_term;
                }
                
                $all_categories = get_terms($categories_args);
                
                if ($all_categories && !is_wp_error($all_categories)) :
                    foreach ($all_categories as $category) :
                        $icon = toolspick_get_category_icon($category->term_id);
                        $color = toolspick_get_category_color($category->term_id);
                        $tools_count = $category->count;
                        $is_featured = get_term_meta($category->term_id, 'category_featured', true);
                        ?>
                        <a href="<?php echo esc_url(get_term_link($category)); ?>" 
                           class="category-card <?php echo $is_featured ? 'is-featured' : ''; ?>"
                           data-category-id="<?php echo esc_attr($category->term_id); ?>"
                           data-tools-count="<?php echo esc_attr($tools_count); ?>"
                           data-category-name="<?php echo esc_attr($category->name); ?>">
                            <div class="category-card-inner">
                                <div class="category-icon-wrapper">
                                    <span class="category-icon" style="color: <?php echo esc_attr($color); ?>">
                                        <?php echo esc_html($icon); ?>
                                    </span>
                                    <?php if ($is_featured) : ?>
                                        <span class="featured-badge">★</span>
                                    <?php endif; ?>
                                </div>
                                <div class="category-info">
                                    <h3 class="category-name"><?php echo esc_html($category->name); ?></h3>
                                    <p class="category-count">
                                        <?php printf(_n('%d tool', '%d tools', $tools_count, 'toolspick'), $tools_count); ?>
                                    </p>
                                    <?php if ($category->description) : ?>
                                        <p class="category-description">
                                            <?php echo esc_html(wp_trim_words($category->description, 12)); ?>
                                        </p>
                                    <?php endif; ?>
                                </div>
                                <div class="category-arrow">
                                    <span class="dashicons dashicons-arrow-right-alt2"></span>
                                </div>
                            </div>
                        </a>
                        <?php
                    endforeach;
                else :
                    ?>
                    <div class="no-categories-found">
                        <p>No categories found matching your search.</p>
                    </div>
                    <?php
                endif;
                ?>
            </div>
        </div>
    </section>

    <!-- Category Statistics -->
    <?php if (get_field('show_statistics_section')) : ?>
    <section class="category-statistics-section">
        <div class="container">
            <div class="section-header">
                <h2><?php echo get_field('statistics_title') ?: 'Category Statistics'; ?></h2>
            </div>
            
            <div class="statistics-grid">
                <div class="stat-card">
                    <div class="stat-number">
                        <?php echo wp_count_terms(['taxonomy' => 'tool_category', 'hide_empty' => false]); ?>
                    </div>
                    <div class="stat-label">Total Categories</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">
                        <?php echo wp_count_terms(['taxonomy' => 'tool_category', 'hide_empty' => true]); ?>
                    </div>
                    <div class="stat-label">Active Categories</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">
                        <?php
                        $featured_count = get_terms([
                            'taxonomy' => 'tool_category',
                            'hide_empty' => true,
                            'meta_query' => [
                                [
                                    'key' => 'category_featured',
                                    'value' => '1',
                                    'compare' => '='
                                ]
                            ],
                            'count' => true
                        ]);
                        echo is_numeric($featured_count) ? $featured_count : count($featured_count);
                        ?>
                    </div>
                    <div class="stat-label">Featured Categories</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">
                        <?php
                        $tools_count = wp_count_posts('tool');
                        echo number_format($tools_count->publish);
                        ?>
                    </div>
                    <div class="stat-label">Total Tools</div>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Custom Content -->
    <?php if (have_posts()) : while (have_posts()) : the_post(); ?>
        <?php if (get_the_content()) : ?>
            <section class="custom-content-section">
                <div class="container">
                    <div class="custom-content">
                        <?php the_content(); ?>
                    </div>
                </div>
            </section>
        <?php endif; ?>
    <?php endwhile; endif; ?>

</main>

<script>
// Category Showcase JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Filter tabs functionality
    const filterTabs = document.querySelectorAll('.filter-tab');
    const categoriesGrid = document.getElementById('categories-grid');
    const categoryCards = document.querySelectorAll('.category-card');
    
    filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs
            filterTabs.forEach(t => t.classList.remove('active'));
            // Add active class to clicked tab
            this.classList.add('active');
            
            const filter = this.dataset.filter;
            filterCategories(filter);
        });
    });
    
    function filterCategories(filter) {
        let sortedCards = Array.from(categoryCards);
        
        switch(filter) {
            case 'popular':
                sortedCards.sort((a, b) => {
                    const countA = parseInt(a.dataset.toolsCount);
                    const countB = parseInt(b.dataset.toolsCount);
                    return countB - countA;
                });
                break;
                
            case 'alphabetical':
                sortedCards.sort((a, b) => {
                    const nameA = a.dataset.categoryName.toLowerCase();
                    const nameB = b.dataset.categoryName.toLowerCase();
                    return nameA.localeCompare(nameB);
                });
                break;
                
            case 'recent':
                // For recent, we'll reverse the default order
                sortedCards.reverse();
                break;
                
            default:
                // 'all' - keep original order
                break;
        }
        
        // Clear and re-append sorted cards
        categoriesGrid.innerHTML = '';
        sortedCards.forEach(card => {
            categoriesGrid.appendChild(card);
        });
        
        // Add animation
        categoriesGrid.style.opacity = '0';
        setTimeout(() => {
            categoriesGrid.style.opacity = '1';
        }, 100);
    }
});
</script>

<style>
/* Category Showcase Specific Styles */
.showcase-header {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.showcase-title {
    font-size: 3rem;
    font-weight: 700;
    margin: 0 0 1rem 0;
}

.showcase-description {
    font-size: 1.25rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.category-search-section {
    padding: 2rem 0;
    background: #f9fafb;
}

.category-search-form {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
}

.featured-categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.featured-category-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.featured-category-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: inherit;
}

.category-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.category-icon-large {
    font-size: 3rem;
    line-height: 1;
}

.category-badge {
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.category-filter-tabs {
    display: flex;
    gap: 1rem;
    margin: 2rem 0;
    justify-content: center;
    flex-wrap: wrap;
}

.filter-tab {
    background: #f3f4f6;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
}

.filter-tab.active,
.filter-tab:hover {
    background: var(--primary-color);
    color: white;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
    transition: opacity 0.3s ease;
}

.category-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
}

.category-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    text-decoration: none;
    color: inherit;
}

.category-card.is-featured {
    border: 2px solid #fbbf24;
}

.category-card-inner {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.category-icon-wrapper {
    position: relative;
    flex-shrink: 0;
}

.category-icon {
    font-size: 2rem;
    line-height: 1;
}

.featured-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #fbbf24;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.category-info {
    flex: 1;
}

.category-name {
    margin: 0 0 0.5rem 0;
    font-size: 1.125rem;
    font-weight: 600;
}

.category-count {
    margin: 0 0 0.5rem 0;
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
}

.category-description {
    margin: 0;
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.4;
}

.category-arrow {
    color: #9ca3af;
    transition: all 0.2s ease;
}

.category-card:hover .category-arrow {
    color: var(--primary-color);
    transform: translateX(4px);
}

.statistics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6b7280;
    font-weight: 500;
}

.no-categories-found {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
    color: #6b7280;
}

@media (max-width: 768px) {
    .showcase-title {
        font-size: 2rem;
    }
    
    .featured-categories-grid {
        grid-template-columns: 1fr;
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
    }
    
    .category-card-inner {
        flex-direction: column;
        text-align: center;
    }
    
    .statistics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<?php get_footer(); ?>

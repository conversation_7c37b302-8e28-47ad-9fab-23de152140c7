<?php
/**
 * Single Tool Template
 * 
 * @package Toolspick
 * @since 1.0.0
 */

get_header();

// Get tool data
$tool_id = get_the_ID();
$short_description = get_field('short_description', $tool_id);
$official_website = get_field('official_website', $tool_id);
$pricing = get_field('pricing', $tool_id);
$pricing_details = get_field('pricing_details', $tool_id);
$features = get_field('features', $tool_id);
$supported_platforms = get_field('supported_platforms', $tool_id);
$integrations = get_field('integrations', $tool_id);
$alternatives = get_field('alternatives', $tool_id);
$rating = get_field('review_rating', $tool_id) ?: 4.0;
$video_tutorial = get_field('video_tutorial', $tool_id);
$affiliate_link = get_field('affiliate_link', $tool_id);
$votes = get_post_meta($tool_id, 'tool_votes', true) ?: 0;
$categories = get_the_terms($tool_id, 'tool_category');
$review_types = get_the_terms($tool_id, 'review_type');
?>

<main id="main" class="site-main single-tool-page">
    
    <!-- Tool Header -->
    <section class="tool-header">
        <div class="container">
            <div class="tool-header-content">
                <?php if (has_post_thumbnail($tool_id)) : ?>
                    <img src="<?php echo esc_url(get_the_post_thumbnail_url($tool_id, 'large')); ?>" 
                         alt="<?php echo esc_attr(get_the_title()); ?> logo" 
                         class="tool-header-logo">
                <?php endif; ?>
                
                <div class="tool-header-info">
                    <h1><?php the_title(); ?></h1>
                    
                    <?php if ($short_description) : ?>
                        <p class="tool-header-description"><?php echo esc_html($short_description); ?></p>
                    <?php endif; ?>
                    
                    <!-- Categories -->
                    <?php if ($categories && !is_wp_error($categories)) : ?>
                        <div class="tool-header-categories">
                            <?php foreach ($categories as $category) : ?>
                                <a href="<?php echo esc_url(get_term_link($category)); ?>" 
                                   class="category-badge">
                                    <?php echo esc_html($category->name); ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Rating and Votes -->
                    <div class="tool-header-meta">
                        <?php if ($rating) : ?>
                            <div class="tool-rating-large">
                                <?php echo toolspick_get_rating_stars($rating, true); ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="vote-container-large">
                            <button class="vote-button vote-button-large" 
                                    data-tool-id="<?php echo esc_attr($tool_id); ?>"
                                    <?php echo toolspick_user_has_voted($tool_id) ? 'disabled' : ''; ?>>
                                <span class="vote-arrow">▲</span>
                                <span class="vote-text">Upvote</span>
                            </button>
                            <span class="vote-count-large"><?php echo number_format($votes); ?> votes</span>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="tool-actions">
                        <?php
                        $primary_link = $affiliate_link ?: $official_website;
                        if ($primary_link) :
                            ?>
                            <a href="<?php echo esc_url($primary_link); ?>" 
                               target="_blank" 
                               rel="noopener <?php echo $affiliate_link ? 'sponsored' : 'nofollow'; ?>"
                               class="btn-primary btn-large">
                                <span class="dashicons dashicons-external"></span>
                                Try <?php the_title(); ?>
                            </a>
                        <?php endif; ?>
                        
                        <?php if ($official_website && $affiliate_link) : ?>
                            <a href="<?php echo esc_url($official_website); ?>" 
                               target="_blank" 
                               rel="noopener nofollow"
                               class="btn-secondary btn-large">
                                Official Website
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tool Content -->
    <section class="tool-content-section">
        <div class="container">
            <div class="tool-content">

                <!-- Main Content -->
                <div class="tool-main-content">

                    <!-- Tool Navigation Tabs -->
                    <div class="tool-nav-tabs">
                        <button class="tab-button active" data-tab="overview">Overview</button>
                        <button class="tab-button" data-tab="features">Features</button>
                        <?php if ($video_tutorial) : ?>
                            <button class="tab-button" data-tab="demo">Demo</button>
                        <?php endif; ?>
                        <?php if ($alternatives && is_array($alternatives)) : ?>
                            <button class="tab-button" data-tab="alternatives">Alternatives</button>
                        <?php endif; ?>
                        <button class="tab-button" data-tab="reviews">Reviews</button>
                    </div>

                    <!-- Tab Content -->
                    <div class="tab-content">

                        <!-- Overview Tab -->
                        <div class="tab-pane active" id="overview">
                            <div class="tool-description">
                                <?php the_content(); ?>
                            </div>

                            <!-- Screenshots/Gallery -->
                            <?php
                            $gallery = get_field('tool_gallery', $tool_id);
                            if ($gallery && is_array($gallery)) :
                                ?>
                                <div class="tool-gallery-section">
                                    <h3>Screenshots</h3>
                                    <div class="tool-gallery">
                                        <?php foreach ($gallery as $index => $image) : ?>
                                            <div class="gallery-item" onclick="openLightbox(<?php echo $index; ?>)">
                                                <img src="<?php echo esc_url($image['sizes']['medium']); ?>"
                                                     alt="<?php echo esc_attr($image['alt']); ?>"
                                                     loading="lazy">
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Key Highlights -->
                            <?php if ($features && is_array($features)) : ?>
                                <div class="key-highlights">
                                    <h3>Key Highlights</h3>
                                    <div class="highlights-grid">
                                        <?php foreach (array_slice($features, 0, 6) as $feature) : ?>
                                            <div class="highlight-item">
                                                <span class="highlight-icon">✓</span>
                                                <span class="highlight-text"><?php echo esc_html($feature['feature_text']); ?></span>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Features Tab -->
                        <div class="tab-pane" id="features">
                            <?php if ($features && is_array($features)) : ?>
                                <div class="detailed-features">
                                    <h3>Detailed Features</h3>
                                    <div class="features-list-detailed">
                                        <?php foreach ($features as $feature) : ?>
                                            <div class="feature-item-detailed">
                                                <div class="feature-icon">
                                                    <span class="dashicons dashicons-yes-alt"></span>
                                                </div>
                                                <div class="feature-content">
                                                    <h4><?php echo esc_html($feature['feature_text']); ?></h4>
                                                    <?php if (isset($feature['feature_description'])) : ?>
                                                        <p><?php echo esc_html($feature['feature_description']); ?></p>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Use Cases -->
                            <?php
                            $use_cases = get_field('use_cases', $tool_id);
                            if ($use_cases && is_array($use_cases)) :
                                ?>
                                <div class="use-cases-section">
                                    <h3>Use Cases</h3>
                                    <div class="use-cases-grid">
                                        <?php foreach ($use_cases as $use_case) : ?>
                                            <div class="use-case-item">
                                                <h4><?php echo esc_html($use_case['title']); ?></h4>
                                                <p><?php echo esc_html($use_case['description']); ?></p>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Demo Tab -->
                        <?php if ($video_tutorial) : ?>
                            <div class="tab-pane" id="demo">
                                <div class="demo-section">
                                    <h3>Product Demo</h3>
                                    <div class="video-embed">
                                        <?php echo wp_oembed_get($video_tutorial); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Alternatives Tab -->
                        <?php if ($alternatives && is_array($alternatives)) : ?>
                            <div class="tab-pane" id="alternatives">
                                <div class="alternatives-section">
                                    <h3>Similar Open Source Alternatives</h3>
                                    <div class="alternatives-comparison">
                                        <?php foreach (array_slice($alternatives, 0, 6) as $alt_id) : ?>
                                            <div class="alternative-comparison-item">
                                                <div class="alternative-header">
                                                    <?php if (has_post_thumbnail($alt_id)) : ?>
                                                        <img src="<?php echo esc_url(get_the_post_thumbnail_url($alt_id, 'thumbnail')); ?>"
                                                             alt="<?php echo esc_attr(get_the_title($alt_id)); ?>"
                                                             class="alternative-logo">
                                                    <?php endif; ?>
                                                    <div class="alternative-info">
                                                        <h4><?php echo esc_html(get_the_title($alt_id)); ?></h4>
                                                        <p class="alternative-description">
                                                            <?php echo esc_html(get_field('short_description', $alt_id)); ?>
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="alternative-details">
                                                    <div class="detail-item">
                                                        <span class="detail-label">Pricing:</span>
                                                        <span class="detail-value"><?php echo esc_html(get_field('pricing', $alt_id) ?: 'Free'); ?></span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <span class="detail-label">Rating:</span>
                                                        <span class="detail-value">
                                                            <?php
                                                            $alt_rating = get_field('review_rating', $alt_id);
                                                            echo $alt_rating ? $alt_rating . '/5' : 'N/A';
                                                            ?>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="alternative-actions">
                                                    <a href="<?php echo esc_url(get_permalink($alt_id)); ?>" class="btn-secondary btn-small">
                                                        View Details
                                                    </a>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Reviews Tab -->
                        <div class="tab-pane" id="reviews">
                            <div class="reviews-section">
                                <h3>User Reviews & Ratings</h3>

                                <!-- Rating Summary -->
                                <div class="rating-summary">
                                    <div class="overall-rating">
                                        <div class="rating-score"><?php echo number_format($rating, 1); ?></div>
                                        <div class="rating-stars">
                                            <?php echo toolspick_get_rating_stars($rating, true); ?>
                                        </div>
                                        <div class="rating-count"><?php echo number_format($votes); ?> votes</div>
                                    </div>

                                    <div class="rating-breakdown">
                                        <?php for ($i = 5; $i >= 1; $i--) : ?>
                                            <div class="rating-bar">
                                                <span class="rating-label"><?php echo $i; ?> stars</span>
                                                <div class="rating-progress">
                                                    <div class="rating-fill" style="width: <?php echo rand(10, 80); ?>%"></div>
                                                </div>
                                                <span class="rating-percentage"><?php echo rand(5, 25); ?>%</span>
                                            </div>
                                        <?php endfor; ?>
                                    </div>
                                </div>

                                <!-- Review Types Links -->
                                <?php if ($review_types && !is_wp_error($review_types)) : ?>
                                    <div class="review-types-links">
                                        <h4>Detailed Reviews</h4>
                                        <div class="review-types-grid">
                                            <?php foreach ($review_types as $review_type) : ?>
                                                <a href="<?php echo esc_url(get_term_link($review_type)); ?>"
                                                   class="review-type-card">
                                                    <?php
                                                    $icon = get_term_meta($review_type->term_id, 'review_icon', true);
                                                    if ($icon) {
                                                        echo '<span class="review-icon">' . esc_html($icon) . '</span>';
                                                    }
                                                    ?>
                                                    <span class="review-type-name"><?php echo esc_html($review_type->name); ?></span>
                                                </a>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <aside class="tool-sidebar">
                    
                    <!-- Quick Info -->
                    <div class="sidebar-card">
                        <h3>Quick Info</h3>
                        <div class="quick-info-list">
                            <?php if ($pricing) : ?>
                                <div class="info-item">
                                    <span class="info-label">Pricing:</span>
                                    <span class="info-value">
                                        <?php echo toolspick_get_pricing_badge($pricing); ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($supported_platforms && is_array($supported_platforms)) : ?>
                                <div class="info-item">
                                    <span class="info-label">Platforms:</span>
                                    <div class="info-value">
                                        <?php echo toolspick_get_platforms_html($supported_platforms); ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php
                            $launch_date = get_field('launch_date', $tool_id);
                            if ($launch_date) :
                                ?>
                                <div class="info-item">
                                    <span class="info-label">Launched:</span>
                                    <span class="info-value"><?php echo date('F Y', strtotime($launch_date)); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Pricing Details -->
                    <?php if ($pricing_details) : ?>
                        <div class="sidebar-card">
                            <h3>Pricing Details</h3>
                            <div class="pricing-content">
                                <?php echo wp_kses_post(nl2br($pricing_details)); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Integrations -->
                    <?php if ($integrations) : ?>
                        <div class="sidebar-card">
                            <h3>Integrations</h3>
                            <div class="integrations-list">
                                <?php
                                $integration_list = array_map('trim', explode(',', $integrations));
                                foreach ($integration_list as $integration) :
                                    ?>
                                    <span class="integration-tag"><?php echo esc_html($integration); ?></span>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Related Tools -->
                    <?php
                    $related_tools = toolspick_get_related_tools($tool_id, 4);
                    if ($related_tools) :
                        ?>
                        <div class="sidebar-card">
                            <h3>Related Tools</h3>
                            <div class="related-tools-list">
                                <?php foreach ($related_tools as $related_tool) : ?>
                                    <a href="<?php echo esc_url(get_permalink($related_tool->ID)); ?>" class="related-tool-item">
                                        <?php if (has_post_thumbnail($related_tool->ID)) : ?>
                                            <img src="<?php echo esc_url(get_the_post_thumbnail_url($related_tool->ID, 'thumbnail')); ?>" 
                                                 alt="<?php echo esc_attr($related_tool->post_title); ?>" 
                                                 class="related-tool-logo">
                                        <?php endif; ?>
                                        <div class="related-tool-info">
                                            <span class="related-tool-name"><?php echo esc_html($related_tool->post_title); ?></span>
                                            <span class="related-tool-pricing">
                                                <?php echo esc_html(get_field('pricing', $related_tool->ID) ?: 'Free'); ?>
                                            </span>
                                        </div>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                </aside>
            </div>
        </div>
    </section>

</main>

<!-- Lightbox Modal -->
<div id="lightbox" class="lightbox" onclick="closeLightbox()">
    <div class="lightbox-content">
        <span class="lightbox-close" onclick="closeLightbox()">&times;</span>
        <img id="lightbox-image" src="" alt="">
        <div class="lightbox-nav">
            <button class="lightbox-prev" onclick="changeLightboxImage(-1)">&#10094;</button>
            <button class="lightbox-next" onclick="changeLightboxImage(1)">&#10095;</button>
        </div>
    </div>
</div>

<script>
// Tab functionality
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.dataset.tab;

            // Remove active class from all buttons and panes
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Add active class to clicked button and corresponding pane
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });
});

// Lightbox functionality
let currentImageIndex = 0;
const galleryImages = <?php
if ($gallery && is_array($gallery)) {
    echo json_encode(array_map(function($image) {
        return [
            'url' => $image['sizes']['large'],
            'alt' => $image['alt']
        ];
    }, $gallery));
} else {
    echo '[]';
}
?>;

function openLightbox(index) {
    currentImageIndex = index;
    const lightbox = document.getElementById('lightbox');
    const lightboxImage = document.getElementById('lightbox-image');

    if (galleryImages[index]) {
        lightboxImage.src = galleryImages[index].url;
        lightboxImage.alt = galleryImages[index].alt;
        lightbox.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }
}

function closeLightbox() {
    const lightbox = document.getElementById('lightbox');
    lightbox.style.display = 'none';
    document.body.style.overflow = 'auto';
}

function changeLightboxImage(direction) {
    currentImageIndex += direction;

    if (currentImageIndex >= galleryImages.length) {
        currentImageIndex = 0;
    } else if (currentImageIndex < 0) {
        currentImageIndex = galleryImages.length - 1;
    }

    const lightboxImage = document.getElementById('lightbox-image');
    lightboxImage.src = galleryImages[currentImageIndex].url;
    lightboxImage.alt = galleryImages[currentImageIndex].alt;
}

// Close lightbox on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeLightbox();
    } else if (e.key === 'ArrowLeft') {
        changeLightboxImage(-1);
    } else if (e.key === 'ArrowRight') {
        changeLightboxImage(1);
    }
});
</script>

<style>
/* Single Tool Page Styles */
.tool-header {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    padding: 2rem 0;
    margin-bottom: 0;
}

.tool-header-content {
    display: flex;
    align-items: flex-start;
    gap: 2rem;
}

.tool-header-logo {
    width: 80px;
    height: 80px;
    border-radius: 12px;
    object-fit: cover;
    border: 1px solid #e5e7eb;
    flex-shrink: 0;
}

.tool-header-info h1 {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    font-weight: 700;
    color: #111827;
}

.tool-header-description {
    font-size: 1rem;
    color: #6b7280;
    margin: 0 0 1rem 0;
    line-height: 1.5;
}

.tool-header-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin: 0 0 1rem 0;
}

.category-badge {
    background: #f3f4f6;
    color: #374151;
    padding: 0.25rem 0.75rem;
    border-radius: 16px;
    text-decoration: none;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.category-badge:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
}

.tool-header-meta {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin: 0 0 1.5rem 0;
}

.tool-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.btn-large {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: 8px;
}

.tool-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    background: white;
}

.tool-main-content {
    background: white;
}

/* Tab Navigation */
.tool-nav-tabs {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 2rem;
    background: white;
    position: sticky;
    top: 0;
    z-index: 10;
}

.tab-button {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.tab-button:hover {
    color: var(--primary-color);
}

.tab-button.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* Tab Content */
.tab-content {
    min-height: 400px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* Tool Description */
.tool-description {
    font-size: 1rem;
    line-height: 1.7;
    color: #374151;
    margin-bottom: 2rem;
}

.tool-description h2,
.tool-description h3 {
    color: #111827;
    margin: 2rem 0 1rem 0;
}

.tool-description p {
    margin-bottom: 1rem;
}

/* Gallery */
.tool-gallery-section {
    margin: 2rem 0;
}

.tool-gallery-section h3 {
    color: #111827;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.tool-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.gallery-item {
    cursor: pointer;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s ease;
}

.gallery-item:hover {
    transform: scale(1.02);
}

.gallery-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

/* Key Highlights */
.key-highlights {
    margin: 2rem 0;
}

.key-highlights h3 {
    color: #111827;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.highlights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.highlight-icon {
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.125rem;
}

.highlight-text {
    font-size: 0.875rem;
    color: #374151;
    font-weight: 500;
}

/* Detailed Features */
.detailed-features h3 {
    color: #111827;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.features-list-detailed {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.feature-item-detailed {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: #f9fafb;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
}

.feature-icon {
    background: var(--primary-color);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.feature-content h4 {
    margin: 0 0 0.5rem 0;
    color: #111827;
    font-size: 1rem;
    font-weight: 600;
}

.feature-content p {
    margin: 0;
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Use Cases */
.use-cases-section {
    margin-top: 2rem;
}

.use-cases-section h3 {
    color: #111827;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.use-cases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.use-case-item {
    padding: 1.5rem;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.use-case-item h4 {
    margin: 0 0 0.75rem 0;
    color: #111827;
    font-size: 1rem;
    font-weight: 600;
}

.use-case-item p {
    margin: 0;
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Demo Section */
.demo-section h3 {
    color: #111827;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

/* Alternatives Section */
.alternatives-section h3 {
    color: #111827;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.alternatives-comparison {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.alternative-comparison-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.alternative-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.alternative-logo {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    object-fit: cover;
    border: 1px solid #e5e7eb;
    flex-shrink: 0;
}

.alternative-info h4 {
    margin: 0 0 0.5rem 0;
    color: #111827;
    font-size: 1rem;
    font-weight: 600;
}

.alternative-description {
    margin: 0;
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.4;
}

.alternative-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.detail-label {
    color: #6b7280;
    font-weight: 500;
}

.detail-value {
    color: #111827;
    font-weight: 600;
}

.alternative-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    border-radius: 6px;
}

/* Reviews Section */
.reviews-section h3 {
    color: #111827;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.rating-summary {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 2rem;
    background: #f9fafb;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.overall-rating {
    text-align: center;
}

.rating-score {
    font-size: 3rem;
    font-weight: 700;
    color: #111827;
    line-height: 1;
}

.rating-stars {
    margin: 0.5rem 0;
}

.rating-count {
    color: #6b7280;
    font-size: 0.875rem;
}

.rating-breakdown {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.rating-bar {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.875rem;
}

.rating-label {
    width: 60px;
    color: #6b7280;
}

.rating-progress {
    flex: 1;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.rating-fill {
    height: 100%;
    background: #fbbf24;
    transition: width 0.3s ease;
}

.rating-percentage {
    width: 40px;
    text-align: right;
    color: #6b7280;
}

.review-types-links {
    margin-top: 2rem;
}

.review-types-links h4 {
    color: #111827;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.review-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.review-type-card {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
}

.review-type-card:hover {
    background: #f9fafb;
    text-decoration: none;
    color: inherit;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.review-icon {
    font-size: 1.5rem;
}

.review-type-name {
    font-weight: 500;
    color: #374151;
}

/* Lightbox */
.lightbox {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    align-items: center;
    justify-content: center;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.lightbox-close {
    position: absolute;
    top: -40px;
    right: 0;
    color: white;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
}

.lightbox-close:hover {
    opacity: 0.7;
}

#lightbox-image {
    max-width: 100%;
    max-height: 100%;
    border-radius: 8px;
}

.lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    pointer-events: none;
}

.lightbox-prev,
.lightbox-next {
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    padding: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    border-radius: 4px;
    pointer-events: all;
    transition: background 0.2s ease;
}

.lightbox-prev:hover,
.lightbox-next:hover {
    background: rgba(0, 0, 0, 0.7);
}

.lightbox-prev {
    margin-left: -60px;
}

.lightbox-next {
    margin-right: -60px;
}

.features-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #e5e7eb;
    position: relative;
    padding-left: 2rem;
}

.features-list li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
    font-size: 1.125rem;
}

.features-list li:last-child {
    border-bottom: none;
}

.video-embed {
    position: relative;
    padding-bottom: 56.25%;
    height: 0;
    overflow: hidden;
    border-radius: 12px;
}

.video-embed iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.review-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.review-type-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 8px;
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
}

.review-type-link:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
}

.review-icon {
    font-size: 1.5rem;
}

.alternatives-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.alternative-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.alternative-item:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.alternative-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    text-decoration: none;
    color: inherit;
}

.alternative-logo {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    object-fit: cover;
    flex-shrink: 0;
}

.alternative-info {
    flex: 1;
}

.alternative-name {
    display: block;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.alternative-pricing {
    font-size: 0.875rem;
    color: #6b7280;
}

.sidebar-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
}

.sidebar-card h3 {
    margin: 0 0 1rem 0;
    color: var(--primary-color);
    font-size: 1.125rem;
    font-weight: 600;
}

.quick-info-list .info-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.quick-info-list .info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 500;
    color: #6b7280;
}

.integrations-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.integration-tag {
    background: #f3f4f6;
    color: #374151;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.related-tools-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.related-tool-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: #f9fafb;
    border-radius: 8px;
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
}

.related-tool-item:hover {
    background: #f3f4f6;
    text-decoration: none;
    color: inherit;
}

.related-tool-logo {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    object-fit: cover;
    flex-shrink: 0;
}

.related-tool-info {
    flex: 1;
}

.related-tool-name {
    display: block;
    font-weight: 500;
    font-size: 0.875rem;
}

.related-tool-pricing {
    font-size: 0.75rem;
    color: #6b7280;
}

@media (max-width: 768px) {
    .tool-header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .tool-header-info h1 {
        font-size: 2rem;
    }
    
    .tool-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .tool-actions {
        justify-content: center;
    }
    
    .alternatives-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php get_footer(); ?>

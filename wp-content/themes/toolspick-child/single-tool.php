<?php
/**
 * Single Tool Template
 * 
 * @package Toolspick
 * @since 1.0.0
 */

get_header();

// Get tool data
$tool_id = get_the_ID();
$short_description = get_field('short_description', $tool_id);
$official_website = get_field('official_website', $tool_id);
$pricing = get_field('pricing', $tool_id);
$pricing_details = get_field('pricing_details', $tool_id);
$features = get_field('features', $tool_id);
$supported_platforms = get_field('supported_platforms', $tool_id);
$integrations = get_field('integrations', $tool_id);
$alternatives = get_field('alternatives', $tool_id);
$rating = get_field('review_rating', $tool_id) ?: 4.0;
$video_tutorial = get_field('video_tutorial', $tool_id);
$affiliate_link = get_field('affiliate_link', $tool_id);
$votes = get_post_meta($tool_id, 'tool_votes', true) ?: 0;
$categories = get_the_terms($tool_id, 'tool_category');
$review_types = get_the_terms($tool_id, 'review_type');
?>

<main id="main" class="site-main single-tool-page">
    
    <!-- Tool Header -->
    <section class="tool-header">
        <div class="container">
            <div class="tool-header-content">
                <?php if (has_post_thumbnail($tool_id)) : ?>
                    <img src="<?php echo esc_url(get_the_post_thumbnail_url($tool_id, 'large')); ?>" 
                         alt="<?php echo esc_attr(get_the_title()); ?> logo" 
                         class="tool-header-logo">
                <?php endif; ?>
                
                <div class="tool-header-info">
                    <h1><?php the_title(); ?></h1>
                    
                    <?php if ($short_description) : ?>
                        <p class="tool-header-description"><?php echo esc_html($short_description); ?></p>
                    <?php endif; ?>
                    
                    <!-- Categories -->
                    <?php if ($categories && !is_wp_error($categories)) : ?>
                        <div class="tool-header-categories">
                            <?php foreach ($categories as $category) : ?>
                                <a href="<?php echo esc_url(get_term_link($category)); ?>" 
                                   class="category-badge">
                                    <?php echo esc_html($category->name); ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Rating and Votes -->
                    <div class="tool-header-meta">
                        <?php if ($rating) : ?>
                            <div class="tool-rating-large">
                                <?php echo toolspick_get_rating_stars($rating, true); ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="vote-container-large">
                            <button class="vote-button vote-button-large" 
                                    data-tool-id="<?php echo esc_attr($tool_id); ?>"
                                    <?php echo toolspick_user_has_voted($tool_id) ? 'disabled' : ''; ?>>
                                <span class="vote-arrow">▲</span>
                                <span class="vote-text">Upvote</span>
                            </button>
                            <span class="vote-count-large"><?php echo number_format($votes); ?> votes</span>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="tool-actions">
                        <?php
                        $primary_link = $affiliate_link ?: $official_website;
                        if ($primary_link) :
                            ?>
                            <a href="<?php echo esc_url($primary_link); ?>" 
                               target="_blank" 
                               rel="noopener <?php echo $affiliate_link ? 'sponsored' : 'nofollow'; ?>"
                               class="btn-primary btn-large">
                                <span class="dashicons dashicons-external"></span>
                                Try <?php the_title(); ?>
                            </a>
                        <?php endif; ?>
                        
                        <?php if ($official_website && $affiliate_link) : ?>
                            <a href="<?php echo esc_url($official_website); ?>" 
                               target="_blank" 
                               rel="noopener nofollow"
                               class="btn-secondary btn-large">
                                Official Website
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tool Content -->
    <section class="tool-content-section">
        <div class="container">
            <div class="tool-content">
                
                <!-- Main Content -->
                <div class="tool-main-content">
                    
                    <!-- Description -->
                    <div class="tool-details">
                        <h2>About <?php the_title(); ?></h2>
                        <div class="tool-description-content">
                            <?php the_content(); ?>
                        </div>
                    </div>

                    <!-- Features -->
                    <?php if ($features && is_array($features)) : ?>
                        <div class="tool-features">
                            <h2>Key Features</h2>
                            <ul class="features-list">
                                <?php foreach ($features as $feature) : ?>
                                    <li><?php echo esc_html($feature['feature_text']); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <!-- Video Tutorial -->
                    <?php if ($video_tutorial) : ?>
                        <div class="tool-video">
                            <h2>Video Tutorial</h2>
                            <div class="video-embed">
                                <?php echo wp_oembed_get($video_tutorial); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Review Types -->
                    <?php if ($review_types && !is_wp_error($review_types)) : ?>
                        <div class="tool-review-types-section">
                            <h2>Explore More</h2>
                            <div class="review-types">
                                <?php foreach ($review_types as $review_type) : ?>
                                    <a href="<?php echo esc_url(get_term_link($review_type)); ?>" 
                                       class="review-type-link">
                                        <?php
                                        $icon = get_term_meta($review_type->term_id, 'review_icon', true);
                                        if ($icon) {
                                            echo '<span class="review-icon">' . esc_html($icon) . '</span>';
                                        }
                                        ?>
                                        <?php echo esc_html($review_type->name); ?>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Alternatives -->
                    <?php if ($alternatives && is_array($alternatives)) : ?>
                        <div class="tool-alternatives">
                            <h2>Alternative Tools</h2>
                            <div class="alternatives-grid">
                                <?php foreach (array_slice($alternatives, 0, 6) as $alt_id) : ?>
                                    <div class="alternative-item">
                                        <a href="<?php echo esc_url(get_permalink($alt_id)); ?>" class="alternative-link">
                                            <?php if (has_post_thumbnail($alt_id)) : ?>
                                                <img src="<?php echo esc_url(get_the_post_thumbnail_url($alt_id, 'thumbnail')); ?>" 
                                                     alt="<?php echo esc_attr(get_the_title($alt_id)); ?> logo" 
                                                     class="alternative-logo">
                                            <?php endif; ?>
                                            <div class="alternative-info">
                                                <span class="alternative-name"><?php echo esc_html(get_the_title($alt_id)); ?></span>
                                                <span class="alternative-pricing">
                                                    <?php echo esc_html(get_field('pricing', $alt_id) ?: 'Free'); ?>
                                                </span>
                                            </div>
                                        </a>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <aside class="tool-sidebar">
                    
                    <!-- Quick Info -->
                    <div class="sidebar-card">
                        <h3>Quick Info</h3>
                        <div class="quick-info-list">
                            <?php if ($pricing) : ?>
                                <div class="info-item">
                                    <span class="info-label">Pricing:</span>
                                    <span class="info-value">
                                        <?php echo toolspick_get_pricing_badge($pricing); ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($supported_platforms && is_array($supported_platforms)) : ?>
                                <div class="info-item">
                                    <span class="info-label">Platforms:</span>
                                    <div class="info-value">
                                        <?php echo toolspick_get_platforms_html($supported_platforms); ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php
                            $launch_date = get_field('launch_date', $tool_id);
                            if ($launch_date) :
                                ?>
                                <div class="info-item">
                                    <span class="info-label">Launched:</span>
                                    <span class="info-value"><?php echo date('F Y', strtotime($launch_date)); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Pricing Details -->
                    <?php if ($pricing_details) : ?>
                        <div class="sidebar-card">
                            <h3>Pricing Details</h3>
                            <div class="pricing-content">
                                <?php echo wp_kses_post(nl2br($pricing_details)); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Integrations -->
                    <?php if ($integrations) : ?>
                        <div class="sidebar-card">
                            <h3>Integrations</h3>
                            <div class="integrations-list">
                                <?php
                                $integration_list = array_map('trim', explode(',', $integrations));
                                foreach ($integration_list as $integration) :
                                    ?>
                                    <span class="integration-tag"><?php echo esc_html($integration); ?></span>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Related Tools -->
                    <?php
                    $related_tools = toolspick_get_related_tools($tool_id, 4);
                    if ($related_tools) :
                        ?>
                        <div class="sidebar-card">
                            <h3>Related Tools</h3>
                            <div class="related-tools-list">
                                <?php foreach ($related_tools as $related_tool) : ?>
                                    <a href="<?php echo esc_url(get_permalink($related_tool->ID)); ?>" class="related-tool-item">
                                        <?php if (has_post_thumbnail($related_tool->ID)) : ?>
                                            <img src="<?php echo esc_url(get_the_post_thumbnail_url($related_tool->ID, 'thumbnail')); ?>" 
                                                 alt="<?php echo esc_attr($related_tool->post_title); ?>" 
                                                 class="related-tool-logo">
                                        <?php endif; ?>
                                        <div class="related-tool-info">
                                            <span class="related-tool-name"><?php echo esc_html($related_tool->post_title); ?></span>
                                            <span class="related-tool-pricing">
                                                <?php echo esc_html(get_field('pricing', $related_tool->ID) ?: 'Free'); ?>
                                            </span>
                                        </div>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                </aside>
            </div>
        </div>
    </section>

</main>

<style>
/* Single Tool Page Styles */
.tool-header {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    color: white;
    padding: 3rem 0;
    margin-bottom: 3rem;
}

.tool-header-content {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.tool-header-logo {
    width: 120px;
    height: 120px;
    border-radius: 20px;
    object-fit: cover;
    background: white;
    padding: 1rem;
    flex-shrink: 0;
}

.tool-header-info h1 {
    margin: 0 0 1rem 0;
    font-size: 2.5rem;
    font-weight: 700;
}

.tool-header-description {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: 0 0 1.5rem 0;
}

.tool-header-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin: 0 0 1.5rem 0;
}

.category-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.category-badge:hover {
    background: rgba(255, 255, 255, 0.3);
    text-decoration: none;
    color: white;
}

.tool-header-meta {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin: 0 0 2rem 0;
}

.tool-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    font-weight: 600;
}

.tool-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
}

.tool-main-content > div {
    margin-bottom: 3rem;
}

.tool-details h2,
.tool-features h2,
.tool-video h2,
.tool-review-types-section h2,
.tool-alternatives h2 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    font-weight: 600;
}

.features-list {
    list-style: none;
    padding: 0;
}

.features-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #e5e7eb;
    position: relative;
    padding-left: 2rem;
}

.features-list li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
    font-size: 1.125rem;
}

.features-list li:last-child {
    border-bottom: none;
}

.video-embed {
    position: relative;
    padding-bottom: 56.25%;
    height: 0;
    overflow: hidden;
    border-radius: 12px;
}

.video-embed iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.review-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.review-type-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 8px;
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
}

.review-type-link:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
}

.review-icon {
    font-size: 1.5rem;
}

.alternatives-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.alternative-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.alternative-item:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.alternative-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    text-decoration: none;
    color: inherit;
}

.alternative-logo {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    object-fit: cover;
    flex-shrink: 0;
}

.alternative-info {
    flex: 1;
}

.alternative-name {
    display: block;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.alternative-pricing {
    font-size: 0.875rem;
    color: #6b7280;
}

.sidebar-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
}

.sidebar-card h3 {
    margin: 0 0 1rem 0;
    color: var(--primary-color);
    font-size: 1.125rem;
    font-weight: 600;
}

.quick-info-list .info-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.quick-info-list .info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 500;
    color: #6b7280;
}

.integrations-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.integration-tag {
    background: #f3f4f6;
    color: #374151;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.related-tools-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.related-tool-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: #f9fafb;
    border-radius: 8px;
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
}

.related-tool-item:hover {
    background: #f3f4f6;
    text-decoration: none;
    color: inherit;
}

.related-tool-logo {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    object-fit: cover;
    flex-shrink: 0;
}

.related-tool-info {
    flex: 1;
}

.related-tool-name {
    display: block;
    font-weight: 500;
    font-size: 0.875rem;
}

.related-tool-pricing {
    font-size: 0.75rem;
    color: #6b7280;
}

@media (max-width: 768px) {
    .tool-header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .tool-header-info h1 {
        font-size: 2rem;
    }
    
    .tool-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .tool-actions {
        justify-content: center;
    }
    
    .alternatives-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php get_footer(); ?>

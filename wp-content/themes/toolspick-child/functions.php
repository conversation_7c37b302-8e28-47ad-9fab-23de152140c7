<?php
/**
 * Toolspick Child Theme Functions
 * 
 * @package Toolspick
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define theme constants
define('TOOLSPICK_VERSION', '1.0.0');
define('TOOLSPICK_THEME_DIR', get_stylesheet_directory());
define('TOOLSPICK_THEME_URL', get_stylesheet_directory_uri());

/**
 * Check for required plugins and add fallbacks
 */
function toolspick_check_requirements() {
    if (!function_exists('get_field')) {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error"><p><strong>Toolspick Child Theme:</strong> Advanced Custom Fields Pro is required for this theme to work properly.</p></div>';
        });

        // Add fallback functions
        if (!function_exists('get_field')) {
            function get_field($field, $post_id = null) {
                return get_post_meta($post_id ?: get_the_ID(), $field, true);
            }
        }

        if (!function_exists('update_field')) {
            function update_field($field, $value, $post_id = null) {
                return update_post_meta($post_id ?: get_the_ID(), $field, $value);
            }
        }
    }
}
add_action('after_setup_theme', 'toolspick_check_requirements');

/**
 * Load theme modules
 */
function toolspick_load_modules() {
    $modules = [
        'enqueue.php',
        'post-type-tool.php',
        'taxonomy-tool-category.php',
        'taxonomy-review-type.php',
        'acf-fields.php',
        'bulk-import.php',
        'helpers.php',
        'voting.php',
        'seo-schema.php'
    ];

    foreach ($modules as $module) {
        $file = TOOLSPICK_THEME_DIR . '/inc/' . $module;
        if (file_exists($file)) {
            require_once $file;
        }
    }
}
add_action('after_setup_theme', 'toolspick_load_modules');

/**
 * Create default pages on theme activation
 */
function toolspick_create_default_pages() {
    // Check if pages already exist to avoid duplicates
    if (get_option('toolspick_pages_created')) {
        return;
    }

    $pages_to_create = [
        [
            'title' => 'Tools Directory',
            'slug' => 'tools',
            'template' => 'page-templates/tools-directory-template.php',
            'content' => 'Browse our comprehensive collection of AI and SaaS tools. Use the filters to find exactly what you need.',
            'meta_fields' => [
                'directory_title' => 'All Tools',
                'directory_description' => 'Discover the best AI and SaaS tools for your business. Compare features, read reviews, and find the perfect solution.',
                'show_categories_section' => 1,
                'categories_section_title' => 'Browse by Category',
                'categories_to_show' => 8
            ]
        ],
        [
            'title' => 'Categories',
            'slug' => 'categories',
            'template' => 'page-templates/category-showcase-template.php',
            'content' => 'Explore all tool categories to find the perfect tools for your specific needs.',
            'meta_fields' => [
                'showcase_title' => 'Tool Categories',
                'showcase_description' => 'Explore our comprehensive collection of tool categories. Find the perfect tools for your specific needs.',
                'show_featured_categories' => 1,
                'featured_section_title' => 'Featured Categories',
                'featured_section_subtitle' => 'Most popular tool categories',
                'featured_categories_count' => 6,
                'all_categories_title' => 'All Categories',
                'all_categories_subtitle' => 'Browse all available tool categories',
                'show_statistics_section' => 1,
                'statistics_title' => 'Category Statistics'
            ]
        ],
        [
            'title' => 'Contact',
            'slug' => 'contact',
            'template' => 'page-templates/contact-template.php',
            'content' => 'Get in touch with us for any questions, tool submissions, or partnership opportunities.',
            'meta_fields' => [
                'contact_title' => 'Get in Touch',
                'contact_description' => 'Have a question about our tools directory? Want to submit a tool? We\'d love to hear from you.',
                'form_title' => 'Send us a Message',
                'info_title' => 'Contact Information',
                'show_newsletter_signup' => 1,
                'show_email_info' => 1,
                'contact_email' => get_option('admin_email'),
                'show_social_info' => 1,
                'show_response_info' => 1,
                'response_time' => '24 hours',
                'show_faq_section' => 1
            ]
        ]
    ];

    $created_pages = [];

    foreach ($pages_to_create as $page_data) {
        // Check if page already exists
        $existing_page = get_page_by_path($page_data['slug']);

        if (!$existing_page) {
            // Create the page
            $page_id = wp_insert_post([
                'post_title' => $page_data['title'],
                'post_name' => $page_data['slug'],
                'post_content' => $page_data['content'],
                'post_status' => 'publish',
                'post_type' => 'page',
                'post_author' => 1
            ]);

            if ($page_id && !is_wp_error($page_id)) {
                // Set the page template
                update_post_meta($page_id, '_wp_page_template', $page_data['template']);

                // Set ACF fields
                if (function_exists('update_field')) {
                    foreach ($page_data['meta_fields'] as $field_name => $field_value) {
                        update_field($field_name, $field_value, $page_id);
                    }
                }

                $created_pages[$page_data['slug']] = $page_id;

                // Add to menu if it exists
                toolspick_add_page_to_menu($page_id, $page_data['title']);
            }
        } else {
            $created_pages[$page_data['slug']] = $existing_page->ID;
        }
    }

    // Store the created page IDs for future reference
    update_option('toolspick_created_pages', $created_pages);
    update_option('toolspick_pages_created', true);

    // Show admin notice
    add_action('admin_notices', function() use ($created_pages) {
        $count = count($created_pages);
        echo '<div class="notice notice-success is-dismissible">';
        echo '<p><strong>Toolspick:</strong> Successfully created ' . $count . ' default pages! ';
        echo 'Visit <a href="' . admin_url('edit.php?post_type=page') . '">Pages</a> to see them.</p>';
        echo '</div>';
    });
}

/**
 * Add page to primary menu if it exists
 */
function toolspick_add_page_to_menu($page_id, $page_title) {
    // Get the primary menu
    $menu_name = 'primary';
    $menu = wp_get_nav_menu_object($menu_name);

    if (!$menu) {
        // Try to find any menu
        $menus = wp_get_nav_menus();
        if (!empty($menus)) {
            $menu = $menus[0];
        }
    }

    if ($menu) {
        // Add page to menu
        wp_update_nav_menu_item($menu->term_id, 0, [
            'menu-item-title' => $page_title,
            'menu-item-object' => 'page',
            'menu-item-object-id' => $page_id,
            'menu-item-type' => 'post_type',
            'menu-item-status' => 'publish'
        ]);
    }
}

// Run on theme activation
add_action('after_switch_theme', 'toolspick_create_default_pages');

/**
 * Add admin menu item to create pages manually
 */
function toolspick_add_setup_menu() {
    add_submenu_page(
        'themes.php',
        'Toolspick Setup',
        'Toolspick Setup',
        'manage_options',
        'toolspick-setup',
        'toolspick_setup_page'
    );
}
add_action('admin_menu', 'toolspick_add_setup_menu');

/**
 * Setup page content
 */
function toolspick_setup_page() {
    if (isset($_POST['create_pages']) && wp_verify_nonce($_POST['setup_nonce'], 'toolspick_setup')) {
        // Reset the flag and create pages
        delete_option('toolspick_pages_created');
        toolspick_create_default_pages();
        echo '<div class="notice notice-success"><p>Pages created successfully!</p></div>';
    }

    $created_pages = get_option('toolspick_created_pages', []);
    ?>
    <div class="wrap">
        <h1>Toolspick Theme Setup</h1>

        <div class="toolspick-setup-container">
            <div class="setup-section">
                <h2>📄 Default Pages</h2>
                <p>Create the essential pages for your tool directory:</p>

                <ul class="setup-pages-list">
                    <li>
                        <strong>Tools Directory</strong> - Main tools listing page with filters
                        <?php if (isset($created_pages['tools'])) : ?>
                            <span class="status-created">✅ Created</span>
                            <a href="<?php echo get_edit_post_link($created_pages['tools']); ?>" class="button-secondary">Edit</a>
                            <a href="<?php echo get_permalink($created_pages['tools']); ?>" class="button-secondary" target="_blank">View</a>
                        <?php endif; ?>
                    </li>
                    <li>
                        <strong>Categories</strong> - Showcase all tool categories
                        <?php if (isset($created_pages['categories'])) : ?>
                            <span class="status-created">✅ Created</span>
                            <a href="<?php echo get_edit_post_link($created_pages['categories']); ?>" class="button-secondary">Edit</a>
                            <a href="<?php echo get_permalink($created_pages['categories']); ?>" class="button-secondary" target="_blank">View</a>
                        <?php endif; ?>
                    </li>
                    <li>
                        <strong>Contact</strong> - Contact form and information
                        <?php if (isset($created_pages['contact'])) : ?>
                            <span class="status-created">✅ Created</span>
                            <a href="<?php echo get_edit_post_link($created_pages['contact']); ?>" class="button-secondary">Edit</a>
                            <a href="<?php echo get_permalink($created_pages['contact']); ?>" class="button-secondary" target="_blank">View</a>
                        <?php endif; ?>
                    </li>
                </ul>

                <form method="post">
                    <?php wp_nonce_field('toolspick_setup', 'setup_nonce'); ?>
                    <button type="submit" name="create_pages" class="button-primary">
                        <?php echo empty($created_pages) ? 'Create Default Pages' : 'Recreate Pages'; ?>
                    </button>
                </form>
            </div>

            <div class="setup-section">
                <h2>🏠 Homepage Setup</h2>
                <p>Set your homepage to use the Toolspick Homepage template:</p>
                <ol>
                    <li>Go to <a href="<?php echo admin_url('edit.php?post_type=page'); ?>">Pages</a></li>
                    <li>Edit your homepage</li>
                    <li>In "Page Attributes", select "Toolspick Homepage" template</li>
                    <li>Customize using the ACF fields that appear</li>
                    <li>Go to <a href="<?php echo admin_url('options-reading.php'); ?>">Settings → Reading</a> and set it as homepage</li>
                </ol>
            </div>

            <div class="setup-section">
                <h2>🔧 Next Steps</h2>
                <ul class="setup-checklist">
                    <li>
                        <a href="<?php echo admin_url('post-new.php?post_type=tool'); ?>">Add your first tool</a>
                    </li>
                    <li>
                        <a href="<?php echo admin_url('edit-tags.php?taxonomy=tool_category&post_type=tool'); ?>">Create tool categories</a>
                    </li>
                    <li>
                        <a href="<?php echo admin_url('edit-tags.php?taxonomy=review_type&post_type=tool'); ?>">Set up review types</a>
                    </li>
                    <li>
                        <a href="<?php echo admin_url('nav-menus.php'); ?>">Update your navigation menu</a>
                    </li>
                    <li>
                        <a href="<?php echo admin_url('tools.php?page=toolspick-bulk-import'); ?>">Import tools in bulk</a>
                    </li>
                </ul>
            </div>

            <div class="setup-section">
                <h2>📚 Available Templates</h2>
                <p>You can create additional pages using these templates:</p>
                <ul>
                    <li><strong>Toolspick Homepage</strong> - Complete homepage with hero, categories, latest tools</li>
                    <li><strong>Tools Directory</strong> - Advanced tools listing with AJAX filters</li>
                    <li><strong>Category Showcase</strong> - Beautiful category display</li>
                    <li><strong>Single Tool Page</strong> - Display any specific tool</li>
                    <li><strong>Contact Page</strong> - Professional contact form</li>
                </ul>
            </div>
        </div>
    </div>

    <style>
        .toolspick-setup-container {
            max-width: 800px;
        }

        .setup-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #ccd0d4;
            box-shadow: 0 1px 1px rgba(0,0,0,.04);
            border-radius: 6px;
        }

        .setup-section h2 {
            margin-top: 0;
            color: #2563eb;
        }

        .setup-pages-list {
            list-style: none;
            padding: 0;
        }

        .setup-pages-list li {
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .setup-pages-list li:last-child {
            border-bottom: none;
        }

        .status-created {
            color: #46b450;
            font-weight: 500;
        }

        .setup-checklist {
            list-style: none;
            padding: 0;
        }

        .setup-checklist li {
            padding: 8px 0;
            position: relative;
            padding-left: 25px;
        }

        .setup-checklist li::before {
            content: "□";
            position: absolute;
            left: 0;
            color: #2563eb;
            font-size: 16px;
        }

        .setup-checklist a {
            text-decoration: none;
            font-weight: 500;
        }

        .setup-checklist a:hover {
            text-decoration: underline;
        }
    </style>
    <?php
}

/**
 * Theme setup
 */
function toolspick_setup() {
    // Add theme support
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    add_theme_support('custom-logo');
    add_theme_support('html5', [
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ]);

    // Set post thumbnail size
    set_post_thumbnail_size(300, 200, true);
    
    // Add custom image sizes
    add_image_size('tool-logo', 80, 80, true);
    add_image_size('tool-card', 300, 200, true);
    add_image_size('tool-hero', 1200, 600, true);
}
add_action('after_setup_theme', 'toolspick_setup');

/**
 * Enqueue parent theme styles
 */
function toolspick_enqueue_parent_styles() {
    wp_enqueue_style('generatepress-style', get_template_directory_uri() . '/style.css');
}
add_action('wp_enqueue_scripts', 'toolspick_enqueue_parent_styles', 1);

/**
 * Enqueue GitHub-style single tool CSS
 */
function toolspick_enqueue_github_styles() {
    if (is_singular('tool')) {
        wp_enqueue_style(
            'toolspick-github-single-tool',
            TOOLSPICK_THEME_URL . '/assets/css/single-tool-github.css',
            ['generatepress-style'],
            TOOLSPICK_VERSION
        );
    }
}
add_action('wp_enqueue_scripts', 'toolspick_enqueue_github_styles');

/**
 * Custom excerpt length
 */
function toolspick_excerpt_length($length) {
    return 25;
}
add_filter('excerpt_length', 'toolspick_excerpt_length');

/**
 * Custom excerpt more
 */
function toolspick_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'toolspick_excerpt_more');

/**
 * Add custom body classes
 */
function toolspick_body_classes($classes) {
    if (is_singular('tool')) {
        $classes[] = 'single-tool-page';
    }
    
    if (is_tax('tool_category')) {
        $classes[] = 'tool-category-page';
    }
    
    if (is_tax('review_type')) {
        $classes[] = 'review-type-page';
    }
    
    return $classes;
}
add_filter('body_class', 'toolspick_body_classes');

/**
 * Modify main query for tool archives
 */
function toolspick_modify_main_query($query) {
    if (!is_admin() && $query->is_main_query()) {
        // Show more tools per page on category pages
        if (is_tax('tool_category') || is_tax('review_type')) {
            $query->set('posts_per_page', 12);
        }
        
        // Order tools by vote count on archives
        if (is_post_type_archive('tool') || is_tax('tool_category')) {
            $query->set('meta_key', 'tool_votes');
            $query->set('orderby', 'meta_value_num');
            $query->set('order', 'DESC');
        }
    }
}
add_action('pre_get_posts', 'toolspick_modify_main_query');

/**
 * Add custom rewrite rules
 */
function toolspick_rewrite_rules() {
    // Custom URL structure for review types
    add_rewrite_rule(
        '^tool/([^/]+)/([^/]+)/?$',
        'index.php?tool=$matches[1]&review_type=$matches[2]',
        'top'
    );
}
add_action('init', 'toolspick_rewrite_rules');

/**
 * Add query vars
 */
function toolspick_query_vars($vars) {
    $vars[] = 'review_type';
    return $vars;
}
add_filter('query_vars', 'toolspick_query_vars');

/**
 * Custom template redirect for review type URLs
 */
function toolspick_template_redirect() {
    $tool_slug = get_query_var('tool');
    $review_type = get_query_var('review_type');
    
    if ($tool_slug && $review_type) {
        $tool = get_page_by_path($tool_slug, OBJECT, 'tool');
        if ($tool) {
            // Set global variables for template use
            global $toolspick_current_tool, $toolspick_current_review_type;
            $toolspick_current_tool = $tool;
            $toolspick_current_review_type = $review_type;
            
            // Load custom template
            include(get_stylesheet_directory() . '/templates/single-tool-review.php');
            exit;
        }
    }
}
add_action('template_redirect', 'toolspick_template_redirect');

/**
 * Register navigation menus
 */
function toolspick_register_menus() {
    register_nav_menus([
        'primary' => __('Primary Menu', 'toolspick'),
        'footer' => __('Footer Menu', 'toolspick'),
        'categories' => __('Categories Menu', 'toolspick'),
    ]);
}
add_action('init', 'toolspick_register_menus');

/**
 * Register widget areas
 */
function toolspick_widgets_init() {
    register_sidebar([
        'name' => __('Tool Sidebar', 'toolspick'),
        'id' => 'tool-sidebar',
        'description' => __('Sidebar for single tool pages', 'toolspick'),
        'before_widget' => '<div id="%1$s" class="widget sidebar-card %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h3 class="widget-title">',
        'after_title' => '</h3>',
    ]);
    
    register_sidebar([
        'name' => __('Footer Widgets', 'toolspick'),
        'id' => 'footer-widgets',
        'description' => __('Footer widget area', 'toolspick'),
        'before_widget' => '<div id="%1$s" class="footer-widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h4 class="footer-widget-title">',
        'after_title' => '</h4>',
    ]);
}
add_action('widgets_init', 'toolspick_widgets_init');

/**
 * Flush rewrite rules on theme activation
 */
function toolspick_flush_rewrites() {
    toolspick_rewrite_rules();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'toolspick_flush_rewrites');

/**
 * Add admin styles
 */
function toolspick_admin_styles() {
    wp_enqueue_style('toolspick-admin', TOOLSPICK_THEME_URL . '/assets/css/admin.css', [], TOOLSPICK_VERSION);
}
add_action('admin_enqueue_scripts', 'toolspick_admin_styles');

/**
 * Add custom post states
 */
function toolspick_display_post_states($post_states, $post) {
    if ($post->post_type === 'tool') {
        $votes = get_post_meta($post->ID, 'tool_votes', true);
        if ($votes) {
            $post_states[] = sprintf(__('%d votes', 'toolspick'), $votes);
        }
    }
    return $post_states;
}
add_filter('display_post_states', 'toolspick_display_post_states', 10, 2);

<?php
/**
 * Tools Archive Template
 * 
 * @package Toolspick
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main tools-archive">
    
    <!-- Archive Header -->
    <section class="archive-header">
        <div class="container">
            <div class="archive-header-content">
                <h1 class="archive-title">All Tools</h1>
                <p class="archive-description">
                    Discover the best AI and SaaS tools for your business. Compare features, read reviews, and find the perfect solution.
                </p>
                
                <div class="archive-stats">
                    <?php
                    $tools_count = wp_count_posts('tool');
                    $categories_count = wp_count_terms(['taxonomy' => 'tool_category', 'hide_empty' => true]);
                    ?>
                    <span class="stat-item">
                        <strong><?php echo number_format($tools_count->publish); ?></strong> Tools
                    </span>
                    <span class="stat-item">
                        <strong><?php echo number_format($categories_count); ?></strong> Categories
                    </span>
                </div>
            </div>
        </div>
    </section>

    <!-- Search and Filters -->
    <section class="tools-filters">
        <div class="container">
            <div class="filters-wrapper">
                
                <!-- Search Form -->
                <form class="tools-search-form" method="get">
                    <input type="search" 
                           name="s" 
                           placeholder="Search tools..." 
                           value="<?php echo esc_attr(get_search_query()); ?>"
                           class="search-input">
                    <input type="hidden" name="post_type" value="tool">
                    <button type="submit" class="search-button">
                        <span class="dashicons dashicons-search"></span>
                    </button>
                </form>

                <!-- Category Filter -->
                <div class="category-filter">
                    <label for="category-filter">Category:</label>
                    <select id="category-filter" name="tool_category" class="category-select" onchange="this.form.submit()">
                        <option value="">All Categories</option>
                        <?php
                        $categories = get_terms([
                            'taxonomy' => 'tool_category',
                            'hide_empty' => true,
                            'orderby' => 'name'
                        ]);
                        
                        foreach ($categories as $category) :
                            ?>
                            <option value="<?php echo esc_attr($category->slug); ?>" 
                                    <?php selected(get_query_var('tool_category'), $category->slug); ?>>
                                <?php echo esc_html($category->name); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Sort Options -->
                <div class="sort-options">
                    <label for="sort-by">Sort by:</label>
                    <select id="sort-by" name="orderby" class="sort-select" onchange="this.form.submit()">
                        <option value="votes" <?php selected(get_query_var('orderby'), 'votes'); ?>>Most Voted</option>
                        <option value="date" <?php selected(get_query_var('orderby'), 'date'); ?>>Newest</option>
                        <option value="title" <?php selected(get_query_var('orderby'), 'title'); ?>>Name A-Z</option>
                        <option value="rating" <?php selected(get_query_var('orderby'), 'rating'); ?>>Highest Rated</option>
                    </select>
                </div>
            </div>
        </div>
    </section>

    <!-- Tools Grid -->
    <section class="tools-grid-section">
        <div class="container">
            
            <?php if (have_posts()) : ?>
                
                <div class="tools-grid-header">
                    <h2>
                        <?php
                        if (is_search()) {
                            printf('Search Results for "%s"', get_search_query());
                        } elseif (get_query_var('tool_category')) {
                            $term = get_term_by('slug', get_query_var('tool_category'), 'tool_category');
                            if ($term) {
                                echo esc_html($term->name) . ' Tools';
                            }
                        } else {
                            echo 'All Tools';
                        }
                        ?>
                    </h2>
                    <div class="results-info">
                        <?php
                        global $wp_query;
                        $total = $wp_query->found_posts;
                        $current_page = max(1, get_query_var('paged'));
                        $per_page = get_query_var('posts_per_page');
                        $start = (($current_page - 1) * $per_page) + 1;
                        $end = min($current_page * $per_page, $total);
                        
                        printf('Showing %d-%d of %d tools', $start, $end, $total);
                        ?>
                    </div>
                </div>

                <div class="tool-grid" id="tools-container">
                    <?php while (have_posts()) : the_post(); ?>
                        <?php include(get_stylesheet_directory() . '/templates/parts/tool-card.php'); ?>
                    <?php endwhile; ?>
                </div>

                <!-- Pagination -->
                <div class="pagination-wrapper">
                    <?php
                    echo paginate_links([
                        'prev_text' => '<span class="dashicons dashicons-arrow-left-alt2"></span> Previous',
                        'next_text' => 'Next <span class="dashicons dashicons-arrow-right-alt2"></span>',
                        'type' => 'list',
                        'class' => 'pagination'
                    ]);
                    ?>
                </div>

            <?php else : ?>
                
                <div class="no-tools-found">
                    <div class="no-tools-content">
                        <span class="no-tools-icon">🔍</span>
                        <h2>No tools found</h2>
                        <?php if (is_search()) : ?>
                            <p>We couldn't find any tools matching your search for "<?php echo esc_html(get_search_query()); ?>".</p>
                            <p>Try adjusting your search terms or <a href="<?php echo esc_url(get_post_type_archive_link('tool')); ?>">browse all tools</a>.</p>
                        <?php else : ?>
                            <p>No tools have been added yet.</p>
                            <?php if (current_user_can('edit_posts')) : ?>
                                <p><a href="<?php echo admin_url('post-new.php?post_type=tool'); ?>" class="btn-primary">Add the first tool</a></p>
                            <?php endif; ?>
                        <?php endif; ?>
                        
                        <div class="no-tools-actions">
                            <a href="<?php echo esc_url(home_url()); ?>" class="btn-primary">
                                Back to Home
                            </a>
                        </div>
                    </div>
                </div>

            <?php endif; ?>
        </div>
    </section>

    <!-- Featured Categories -->
    <section class="featured-categories-section">
        <div class="container">
            <h2>Browse by Category</h2>
            <div class="categories-grid">
                <?php
                $featured_categories = toolspick_get_featured_categories(8);
                
                if ($featured_categories) :
                    foreach ($featured_categories as $category) :
                        $icon = toolspick_get_category_icon($category->term_id);
                        $color = toolspick_get_category_color($category->term_id);
                        ?>
                        <a href="<?php echo esc_url(get_term_link($category)); ?>" 
                           class="category-card"
                           style="border-top: 4px solid <?php echo esc_attr($color); ?>">
                            <span class="category-icon"><?php echo esc_html($icon); ?></span>
                            <h3 class="category-name"><?php echo esc_html($category->name); ?></h3>
                            <p class="category-count">
                                <?php printf(_n('%d tool', '%d tools', $category->count, 'toolspick'), $category->count); ?>
                            </p>
                        </a>
                        <?php
                    endforeach;
                endif;
                ?>
            </div>
        </div>
    </section>

</main>

<style>
/* Archive Page Specific Styles */
.archive-header {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
    text-align: center;
}

.archive-title {
    margin: 0 0 1rem 0;
    font-size: 2.5rem;
    font-weight: 700;
}

.archive-description {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: 0 0 2rem 0;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.archive-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.stat-item {
    font-size: 0.875rem;
    opacity: 0.9;
}

.tools-filters {
    background: #f9fafb;
    padding: 1.5rem 0;
    margin-bottom: 2rem;
}

.filters-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    align-items: center;
}

.tools-search-form {
    display: flex;
    flex: 1;
    max-width: 400px;
}

.search-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px 0 0 8px;
    font-size: 0.875rem;
}

.search-button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 0 8px 8px 0;
    cursor: pointer;
}

.category-filter, .sort-options {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.category-select, .sort-select {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
}

.tools-grid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.results-info {
    color: #6b7280;
    font-size: 0.875rem;
}

.no-tools-found {
    text-align: center;
    padding: 4rem 0;
}

.no-tools-icon {
    font-size: 4rem;
    display: block;
    margin-bottom: 1rem;
}

.no-tools-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.featured-categories-section {
    background: #f9fafb;
    padding: 3rem 0;
    margin-top: 3rem;
}

.featured-categories-section h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--primary-color);
    font-size: 2rem;
    font-weight: 600;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.category-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    text-align: center;
}

.category-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    text-decoration: none;
    color: inherit;
}

.category-icon {
    font-size: 3rem;
    display: block;
    margin-bottom: 1rem;
}

.category-name {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
}

.category-count {
    margin: 0;
    color: #6b7280;
    font-size: 0.875rem;
}

.pagination-wrapper {
    margin-top: 3rem;
    text-align: center;
}

.pagination {
    display: inline-flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0.5rem;
}

.pagination li {
    margin: 0;
}

.pagination a,
.pagination span {
    display: block;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    text-decoration: none;
    color: #374151;
    transition: all 0.2s ease;
}

.pagination a:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .current {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

@media (max-width: 768px) {
    .archive-title {
        font-size: 2rem;
    }
    
    .archive-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .filters-wrapper {
        flex-direction: column;
        align-items: stretch;
    }
    
    .tools-search-form {
        max-width: none;
    }
    
    .tools-grid-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php get_footer(); ?>

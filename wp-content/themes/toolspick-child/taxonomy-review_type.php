<?php
/**
 * Review Type Archive Template
 * 
 * @package Toolspick
 * @since 1.0.0
 */

get_header();

$term = get_queried_object();
$review_icon = get_term_meta($term->term_id, 'review_icon', true) ?: '📝';
?>

<main id="main" class="site-main review-type-archive">
    
    <!-- Review Type Header -->
    <section class="review-type-header">
        <div class="container">
            <div class="review-type-header-content">
                <span class="review-type-icon-large"><?php echo esc_html($review_icon); ?></span>
                
                <div class="review-type-header-info">
                    <h1 class="review-type-title"><?php echo esc_html($term->name); ?></h1>
                    
                    <?php if ($term->description) : ?>
                        <p class="review-type-description"><?php echo esc_html($term->description); ?></p>
                    <?php endif; ?>
                    
                    <div class="review-type-stats">
                        <span class="stat-item">
                            <strong><?php echo number_format($term->count); ?></strong> 
                            <?php printf(_n('Tool', 'Tools', $term->count, 'toolspick'), $term->count); ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Breadcrumbs -->
    <section class="breadcrumbs-section">
        <div class="container">
            <?php echo toolspick_get_breadcrumbs(); ?>
        </div>
    </section>

    <!-- Search and Filters -->
    <section class="review-type-filters">
        <div class="container">
            <div class="filters-wrapper">
                
                <!-- Search Form -->
                <form class="review-type-search-form" method="get">
                    <input type="search" 
                           name="s" 
                           placeholder="Search <?php echo esc_attr($term->name); ?>..." 
                           value="<?php echo esc_attr(get_search_query()); ?>"
                           class="search-input">
                    <input type="hidden" name="post_type" value="tool">
                    <input type="hidden" name="review_type" value="<?php echo esc_attr($term->slug); ?>">
                    <button type="submit" class="search-button">
                        <span class="dashicons dashicons-search"></span>
                    </button>
                </form>

                <!-- Category Filter -->
                <div class="category-filter">
                    <label for="category-filter">Category:</label>
                    <select id="category-filter" name="tool_category" class="category-select" onchange="this.form.submit()">
                        <option value="">All Categories</option>
                        <?php
                        $categories = get_terms([
                            'taxonomy' => 'tool_category',
                            'hide_empty' => true,
                            'orderby' => 'name'
                        ]);
                        
                        foreach ($categories as $category) :
                            ?>
                            <option value="<?php echo esc_attr($category->slug); ?>" 
                                    <?php selected(get_query_var('tool_category'), $category->slug); ?>>
                                <?php echo esc_html($category->name); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Sort Options -->
                <div class="sort-options">
                    <label for="sort-by">Sort by:</label>
                    <select id="sort-by" name="orderby" class="sort-select" onchange="this.form.submit()">
                        <option value="votes" <?php selected(get_query_var('orderby'), 'votes'); ?>>Most Voted</option>
                        <option value="date" <?php selected(get_query_var('orderby'), 'date'); ?>>Newest</option>
                        <option value="title" <?php selected(get_query_var('orderby'), 'title'); ?>>Name A-Z</option>
                        <option value="rating" <?php selected(get_query_var('orderby'), 'rating'); ?>>Highest Rated</option>
                    </select>
                </div>
            </div>
        </div>
    </section>

    <!-- Tools Grid -->
    <section class="review-type-tools-section">
        <div class="container">
            
            <?php if (have_posts()) : ?>
                
                <div class="review-type-tools-header">
                    <h2>
                        <?php
                        if (is_search()) {
                            printf('Search Results in %s', $term->name);
                        } else {
                            printf('Tools with %s', $term->name);
                        }
                        ?>
                    </h2>
                    <div class="results-info">
                        <?php
                        global $wp_query;
                        $total = $wp_query->found_posts;
                        $current_page = max(1, get_query_var('paged'));
                        $per_page = get_query_var('posts_per_page');
                        $start = (($current_page - 1) * $per_page) + 1;
                        $end = min($current_page * $per_page, $total);
                        
                        printf('Showing %d-%d of %d tools', $start, $end, $total);
                        ?>
                    </div>
                </div>

                <div class="tool-grid" id="tools-container">
                    <?php while (have_posts()) : the_post(); ?>
                        <article class="tool-card review-type-card">
                            <?php include(get_stylesheet_directory() . '/templates/parts/tool-card.php'); ?>
                            
                            <!-- Review Type Badge -->
                            <div class="review-type-badge">
                                <span class="review-badge-icon"><?php echo esc_html($review_icon); ?></span>
                                <span class="review-badge-text"><?php echo esc_html($term->name); ?></span>
                            </div>
                        </article>
                    <?php endwhile; ?>
                </div>

                <!-- Pagination -->
                <div class="pagination-wrapper">
                    <?php
                    echo paginate_links([
                        'prev_text' => '<span class="dashicons dashicons-arrow-left-alt2"></span> Previous',
                        'next_text' => 'Next <span class="dashicons dashicons-arrow-right-alt2"></span>',
                        'type' => 'list',
                        'class' => 'pagination'
                    ]);
                    ?>
                </div>

            <?php else : ?>
                
                <div class="no-tools-found">
                    <div class="no-tools-content">
                        <span class="no-tools-icon"><?php echo esc_html($review_icon); ?></span>
                        <h2>No tools found</h2>
                        <?php if (is_search()) : ?>
                            <p>We couldn't find any tools with <?php echo esc_html($term->name); ?> matching your search.</p>
                        <?php else : ?>
                            <p>No tools have been tagged with <?php echo esc_html($term->name); ?> yet.</p>
                        <?php endif; ?>
                        
                        <div class="no-tools-actions">
                            <a href="<?php echo esc_url(get_post_type_archive_link('tool')); ?>" class="btn-primary">
                                Browse All Tools
                            </a>
                        </div>
                    </div>
                </div>

            <?php endif; ?>
        </div>
    </section>

    <!-- Other Review Types -->
    <?php
    $other_review_types = get_terms([
        'taxonomy' => 'review_type',
        'hide_empty' => true,
        'exclude' => [$term->term_id],
        'number' => 6,
        'orderby' => 'count',
        'order' => 'DESC'
    ]);
    
    if ($other_review_types && !is_wp_error($other_review_types)) :
        ?>
        <section class="other-review-types-section">
            <div class="container">
                <h2>Other Review Types</h2>
                <div class="review-types-grid">
                    <?php foreach ($other_review_types as $other_type) : ?>
                        <?php
                        $other_icon = get_term_meta($other_type->term_id, 'review_icon', true) ?: '📝';
                        ?>
                        <a href="<?php echo esc_url(get_term_link($other_type)); ?>" 
                           class="review-type-card-link">
                            <div class="review-type-card-content">
                                <span class="review-type-icon"><?php echo esc_html($other_icon); ?></span>
                                <h3 class="review-type-name"><?php echo esc_html($other_type->name); ?></h3>
                                <p class="review-type-count">
                                    <?php printf(_n('%d tool', '%d tools', $other_type->count, 'toolspick'), $other_type->count); ?>
                                </p>
                                <?php if ($other_type->description) : ?>
                                    <p class="review-type-desc"><?php echo esc_html(wp_trim_words($other_type->description, 12)); ?></p>
                                <?php endif; ?>
                            </div>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
    <?php endif; ?>

</main>

<style>
/* Review Type Archive Specific Styles */
.review-type-header {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
}

.review-type-header-content {
    display: flex;
    align-items: center;
    gap: 2rem;
    text-align: left;
}

.review-type-icon-large {
    font-size: 4rem;
    line-height: 1;
    flex-shrink: 0;
}

.review-type-title {
    margin: 0 0 1rem 0;
    font-size: 2.5rem;
    font-weight: 700;
}

.review-type-description {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: 0 0 1.5rem 0;
}

.review-type-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    font-size: 0.875rem;
    opacity: 0.9;
}

.breadcrumbs-section {
    background: #f9fafb;
    padding: 1rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.review-type-filters {
    background: #f9fafb;
    padding: 1.5rem 0;
    margin-bottom: 2rem;
}

.filters-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    align-items: center;
}

.review-type-search-form {
    display: flex;
    flex: 1;
    max-width: 400px;
}

.search-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px 0 0 8px;
    font-size: 0.875rem;
}

.search-button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 0 8px 8px 0;
    cursor: pointer;
}

.category-filter, .sort-options {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.category-select, .sort-select {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
}

.review-type-tools-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.results-info {
    color: #6b7280;
    font-size: 0.875rem;
}

.review-type-card {
    position: relative;
}

.review-type-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(99, 102, 241, 0.9);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    backdrop-filter: blur(10px);
}

.review-badge-icon {
    font-size: 0.875rem;
}

.no-tools-found {
    text-align: center;
    padding: 4rem 0;
}

.no-tools-icon {
    font-size: 4rem;
    display: block;
    margin-bottom: 1rem;
}

.no-tools-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.other-review-types-section {
    background: #f9fafb;
    padding: 3rem 0;
    margin-top: 3rem;
}

.other-review-types-section h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--primary-color);
    font-size: 1.75rem;
    font-weight: 600;
}

.review-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.review-type-card-link {
    background: white;
    border-radius: 12px;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.review-type-card-link:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    text-decoration: none;
    color: inherit;
}

.review-type-card-content {
    padding: 2rem;
    text-align: center;
}

.review-type-icon {
    font-size: 3rem;
    display: block;
    margin-bottom: 1rem;
}

.review-type-name {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
}

.review-type-count {
    margin: 0 0 1rem 0;
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
}

.review-type-desc {
    margin: 0;
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.4;
}

@media (max-width: 768px) {
    .review-type-header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .review-type-title {
        font-size: 2rem;
    }
    
    .filters-wrapper {
        flex-direction: column;
        align-items: stretch;
    }
    
    .review-type-search-form {
        max-width: none;
    }
    
    .review-type-tools-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .review-types-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php get_footer(); ?>

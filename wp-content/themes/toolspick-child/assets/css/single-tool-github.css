/* GitHub Style Single Tool Page */

.single-tool-github {
    background: #ffffff;
    min-height: 100vh;
}

/* Tool Header */
.tool-header-github {
    background: #ffffff;
    border-bottom: 1px solid #e1e4e8;
    padding: 24px 0;
}

.tool-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 24px;
}

.tool-title-section {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    flex: 1;
}

.tool-logo {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    object-fit: cover;
    border: 1px solid #e1e4e8;
}

.tool-title-info {
    flex: 1;
}

.tool-title {
    font-size: 32px;
    font-weight: 600;
    color: #24292f;
    margin: 0 0 8px 0;
    line-height: 1.25;
}

.tool-description {
    font-size: 16px;
    color: #656d76;
    margin: 0;
    line-height: 1.5;
}

/* Action Buttons */
.tool-actions-github {
    display: flex;
    align-items: flex-start;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.btn-github {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 16px;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    border: 1px solid #d0d7de;
    border-radius: 6px;
    background: #f6f8fa;
    color: #24292f;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-github:hover {
    background: #f3f4f6;
    border-color: #d0d7de;
    text-decoration: none;
    color: #24292f;
}

.btn-github .btn-icon {
    font-size: 14px;
}

.btn-claim {
    background: #0969da;
    color: white;
    border-color: #0969da;
}

.btn-claim:hover {
    background: #0860ca;
    color: white;
}

.btn-visit {
    background: #1f883d;
    color: white;
    border-color: #1f883d;
}

.btn-visit:hover {
    background: #1a7f37;
    color: white;
}

/* Main Content Layout */
.tool-content-github {
    padding: 32px 0;
}

.content-layout-github {
    display: grid;
    grid-template-columns: 1fr 296px;
    gap: 32px;
    align-items: start;
}

/* Main Content */
.main-content-github {
    min-width: 0;
}

.tool-description-section {
    margin-bottom: 32px;
}

.tool-description-section p {
    font-size: 16px;
    line-height: 1.6;
    color: #24292f;
    margin-bottom: 16px;
}

/* Gallery */
.tool-gallery-github {
    margin-bottom: 32px;
}

.gallery-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
}

.gallery-item {
    border: 1px solid #e1e4e8;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.gallery-item:hover {
    border-color: #d0d7de;
}

.gallery-item img {
    width: 100%;
    height: auto;
    display: block;
}

/* Features */
.features-section-github {
    margin-bottom: 32px;
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    font-size: 16px;
    line-height: 1.6;
}

.feature-bullet {
    color: #656d76;
    font-weight: bold;
    margin-top: 2px;
}

.feature-text {
    color: #24292f;
}

/* Taxonomy */
.taxonomy-section-github {
    margin-bottom: 32px;
}

.categories-section,
.tags-section {
    margin-bottom: 16px;
}

.categories-section h4,
.tags-section h4 {
    font-size: 14px;
    font-weight: 600;
    color: #24292f;
    margin: 0 0 8px 0;
}

.taxonomy-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.taxonomy-tag {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    background: #f6f8fa;
    color: #24292f;
    border: 1px solid #d0d7de;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.2s ease;
}

.taxonomy-tag:hover {
    background: #f3f4f6;
    text-decoration: none;
    color: #24292f;
}

.tag-style {
    background: #ddf4ff;
    color: #0969da;
    border-color: #54aeff;
}

.tag-style:hover {
    background: #b6e3ff;
    color: #0969da;
}

/* Sidebar */
.sidebar-github {
    position: sticky;
    top: 24px;
}

.details-card {
    background: #ffffff;
    border: 1px solid #e1e4e8;
    border-radius: 8px;
    padding: 16px;
}

.details-card h3 {
    font-size: 16px;
    font-weight: 600;
    color: #24292f;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #e1e4e8;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;
    border-bottom: 1px solid #f6f8fa;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.detail-label {
    font-size: 14px;
    color: #656d76;
    flex: 1;
}

.detail-value {
    font-size: 14px;
    font-weight: 500;
    color: #24292f;
}

.pricing-badge {
    background: #fff8dc;
    color: #bf8700;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    text-transform: uppercase;
}

.platform-badge {
    display: inline-block;
    background: #f6f8fa;
    color: #24292f;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    margin: 2px 4px 2px 0;
}

.btn-full-width {
    width: 100%;
    justify-content: center;
    margin-top: 8px;
}

/* Lightbox */
.lightbox {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    align-items: center;
    justify-content: center;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.lightbox-close {
    position: absolute;
    top: -40px;
    right: 0;
    color: white;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
    background: none;
    border: none;
}

.lightbox-close:hover {
    opacity: 0.7;
}

#lightbox-image {
    max-width: 100%;
    max-height: 100%;
    border-radius: 8px;
}

.lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    pointer-events: none;
}

.lightbox-prev,
.lightbox-next {
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    padding: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    border-radius: 4px;
    pointer-events: all;
    transition: background 0.2s ease;
}

.lightbox-prev:hover,
.lightbox-next:hover {
    background: rgba(0, 0, 0, 0.7);
}

.lightbox-prev {
    margin-left: -60px;
}

.lightbox-next {
    margin-right: -60px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .content-layout-github {
        grid-template-columns: 1fr;
        gap: 24px;
    }
    
    .sidebar-github {
        position: static;
        order: -1;
    }
}

@media (max-width: 768px) {
    .tool-header-content {
        flex-direction: column;
        gap: 16px;
    }
    
    .tool-title-section {
        width: 100%;
    }
    
    .tool-actions-github {
        width: 100%;
    }
    
    .action-buttons {
        width: 100%;
        justify-content: flex-start;
    }
    
    .tool-title {
        font-size: 24px;
    }
    
    .gallery-container {
        grid-template-columns: 1fr;
    }
}
